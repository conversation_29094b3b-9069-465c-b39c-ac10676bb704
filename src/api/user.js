import { MANAGERURL, LIVEURL, APIURL } from '@/utils/configData/config'
import request from '@/utils/request'

/**
 * 用户账号相关
 * @param data
 */
export function getUserAccountCorrelation(data) {
  return request({
    url: '/uic/oss/member/listUsers',
    method: 'post',
    data
  })
}

/**
 * 用户账号相关
 * @param data
 */
export function getUserAccountCorrelationV2(data) {
  return request({
    url: '/user/center/oss/userInfo/listUsersV2',
    method: 'post',
    data
  })
}
/**
 * 用户id查询用户认证信息
 * @param data
 */
export function getUserCertify(params) {
  return request({
    url: '/uic/oss/accountType/getUserCertify',
    method: 'get',
    params
  })
}

/**
 * 用户账号相关
 * @param data
 */
export function selectedUser(data) {
  return request({
    url: '/user/center/oss/userInfo/listUsersV3',
    method: 'post',
    data
  })
}

/**
 * 获取用户账号详情
 * @param data
 */
export function getUserAccountDetails(data) {
  return request({
    url: '/user/center/oss/userInfo/details',
    method: 'post',
    data
  })
}

/**
 * 获取设备账号详情
 * @param data
 */
export function getDeviceDetails(data) {
  return request({
    url: '/uic/oss/member/device/details',
    method: 'post',
    data
  })
}

/**
 * 更新用户账号相关
 * @param data
 */
export function updataUserAccountCorrelation(data) {
  return request({
    url: '/user/center/oss/userInfo/modifyInfo',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10301',
    data
  })
}

/**
 * 导入背包碎片
 * @param data
 */
export function postPrizebatchSave(data) {
  return request({
    url: '/coins/oss/prize/batchSave',
    method: 'post',
    data
  })
}

// 用户保单列表
export function getIllegalList(params) {
  return request({
    url: '/uic/oss/insurance/list',
    method: 'get',
    params
  })
}

// 举报概览列表
export function getReportSummary(params) {
  return request({
    url: '/forum/oss/reportController/reportOverview',
    method: 'get',
    params
  })
}

// 举报明细列表
export function getReportDetail(params) {
  return request({
    url: '/forum/oss/reportController/reportList',
    method: 'get',
    params
  })
}

// 撤销操作
export function batchHandleRevocable(data) {
  return request({
    url: '/forum/oss/reportController/batchHandleRevocable',
    method: 'post',
    data
  })
}

// 二手车处罚管理新增处罚 商家
export function punishmentMerchants(data) {
  return request({
    url: '/carport/oss/punish/businessPunish',
    method: 'post',
    data
  })
}

// 二手车处罚管理新增处罚
export function newPenalties(data) {
  return request({
    url: '/shop/oss/violation/punish/create/punish',
    method: 'post',
    data
  })
}

// 二手车处罚管理处理
export function handlePunish(data) {
  return request({
    url: '/shop/oss/violation/punish/handle/punish',
    method: 'post',
    data
  })
}

// 下架
export function batchHandleReport(data) {
  return request({
    url: '/forum/oss/reportController/batchHandleReport',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

// 批量处理举报
export function secondHandCarReport(data) {
  return request({
    url: '/forum/oss/reportController/secondHandCar/batchHandleReport',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

// 单个处理举报
export function reportHandleRelation(data) {
  return request({
    url: '/forum/oss/reportController/handleSingleReport',
    method: 'post',
    beforeAlterTwo: false,
    data
  })
}

/**
 * 新增推荐用户
 * @param data
 */
export function createUser(data) {
  return request({
    url: '/forum/oss/recommendUser/createUser',
    method: 'post',
    data
  })
}

/**
 * 删除关注用户
 * @param data
 */
export function deleteUser(data) {
  return request({
    url: '/forum/oss/recommendUser/deleteUser',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

/**
 * 修改标签
 * @param data
 */
export function updatePersonLabel(data) {
  return request({
    url: '/forum/oss/recommendUser/updateLabel',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

// 推荐用户列表
export function getSelectList(params) {
  return request({
    url: '/forum/oss/recommendUser/selectList',
    method: 'get',
    params
  })
}

// 骑行列表
export function getTraceList(params) {
  return request({
    url: '/carport/oss/trace/list',
    method: 'get',
    params
  })
}

/**
 * 删除骑行记录
 * @param data
 */
export function delTraceDelete(data) {
  return request({
    url: '/carport/oss/trace/delete',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10306',
    data
  })
}

/**
 * 无效骑行记录
 * @param data
 */
export function updateTraceInvalid(data) {
  return request({
    url: '/carport/oss/trace/invalid',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10306',
    data
  })
}

/**
 * 异常列表
 * @param params
 */
export function getWarningList(params) {
  return request({
    url: '/riskcontrol/oss/action/warning/list',
    method: 'get',
    params
  })
}

/**
 * 用户私信列表
 * @param data
 */
export function getUserPmList(params) {
  return request({
    url: '/uic/oss/message/userPmList',
    method: 'get',
    params
  })
}

/**
 * 查看评论异常
 * @param data
 */
export function showCommentError({ autherId, page, limit }) {
  return request({
    url: '/reply/oss/auth/replyByUid',
    method: 'get',
    params: {
      autherId,
      page,
      limit
    }
  })
}

/**
 * 异常用户处理
 * @param data
 */
export function postHandle(data) {
  return request({
    url: '/riskcontrol/oss/action/warning/handle',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

/**
 * 异常用户处理，
 * 该用户发表的简介、评论、文章、动态..等内容全部删除（除私信），不触发提醒
 * @param data
 */
export function deleteUserContent({ uid, operator }) {
  return request({
    url: '/forum/oss/action/warning/banUserPublishedEver',
    method: 'post',
    beforeAlterFirst: false,
    data: {
      uid,
      operator
    }
  })
}

/**
 * 异常用户批处理
 * @param data
 */
export function updataBatchHadle(data) {
  return request({
    url: '/riskcontrol/oss/action/warning/batchHadle',
    method: 'post',
    data
  })
}

/**
 * 提交线索异常 询价，试驾，电话，一口价相关
 * @param data
 */
export function getQueryList(params) {
  return request({
    url: '/shop/oss/query/price/getQueryList',
    method: 'get',
    params
  })
}

/**
 * 举报人员列表
 * @param data
 */
export function getWhistleblowersList(params) {
  return request({
    url: '/uic/oss/limit/list',
    method: 'get',
    params
  })
}

/**
 * 获取封禁ip/设备列表
 */
export function forbidList(params) {
  return request({
    url: '/riskcontrol/oss/forbid/list',
    method: 'get',
    params
  })
}

/**
 * 拉黑/禁言
 * @param data
 */
export function updataLimituser(data) {
  return request({
    url: '/uic/oss/member/limituser',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10301',
    data
  })
}

/**
 * 添加封禁ip/设备
 */
export function addForbidList(data) {
  return request({
    url: '/riskcontrol/oss/forbid/insertForbid',
    method: 'post',
    data
  })
}

/**
 * 取消用户禁言
 * @param data
 */
export function changeNormalGroup(data) {
  return request({
    url: '/uic/oss/member/changeNormalGroup',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10311',
    data
  })
}

/**
 * 封禁日志列表
 * @param params
 */
export function queryUserLimitLog(params) {
  return request({
    url: '/uic/oss/member/queryUserLimitLog',
    method: 'get',
    params
  })
}

/**
 * 更新封禁ip/设备
 */
export function updateForbidStatus(data) {
  return request({
    url: '/riskcontrol/oss/forbid/invalidForbid',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10310',
    data
  })
}

/**
 * 推送文库列表
 * @param data
 */
export function getPushLibrary(url) {
  return request({
    url: `${MANAGERURL}forum/oss/essayPushRepo/essayList`,
    method: 'get',
    params: url
  })
}

/**
 * 文库推送-标题内容配置
 * @param data
 */
export function setPushTitle(data) {
  return request({
    url: `${MANAGERURL}forum/oss/essayPushRepo/saveConf`,
    method: 'post',
    data
  })
}

/**
 * 移除推送
 * @param data
 */
export function removePush(data) {
  return request({
    url: `${MANAGERURL}forum/oss/essayPushRepo/deleteConf`,
    method: 'post',
    data
  })
}

/**
 * 个性化推送列表
 */
export function getPushManagerList(params) {
  return request({
    url: `${MANAGERURL}push/manager/list`,
    method: 'get',
    params
  })
}

/**
 * 查看个性化推送
 */
export function getPushManagerDetail(params) {
  return request({
    url: `${MANAGERURL}push/manager/detail`,
    method: 'get',
    params
  })
}

/**
 * 撤回个性化推送
 * @param data
 */
export function RecallPushManager(data) {
  return request({
    url: `${MANAGERURL}push/manager/recall`,
    method: 'post',
    data
  })
}

/**
 * 保存个性化推送
 * @param data
 */
export function SavePushManager(data) {
  return request({
    url: `${MANAGERURL}push/manager/save`,
    method: 'post',
    data
  })
}

/**
 * 商家端推送列表
 */
export function getShopPushManagerList(params) {
  return request({
    url: `/push/shop/manager/list`,
    method: 'get',
    params
  })
}

/**
 * 商家版推送-详情
 * @param params
 */
export function getShopPushManagerDetail(id) {
  return request({
    url: `/push/shop/manager/detail`,
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * 商家版推送 - 新增/修改
 */
export function postShopPushManagerSave(data) {
  return request({
    url: `/push/shop/manager/save`,
    method: 'post',
    data
  })
}

/**
 * 商家版推送-撤回
 * @param data
 */
export function postShopPushManagerRecall(data) {
  return request({
    url: `/push/shop/manager/recall`,
    method: 'post',
    data
  })
}

/**
 * 查看手机号
 */
export function getMobile(data) {
  return request({
    url: `/uic/oss/mobile/sensitive/commonDecryptMobile`,
    method: 'post',
    data
  })
}

/**
 * 私信列表查询接口
 */
export function GetMessageLastMessageList(params) {
  return request({
    url: `/uic/oss/message/lastMessageList`,
    method: 'get',
    params
  })
}

/**
 * 禁用词列表查询
 */
export function GetStopWordList(params) {
  return request({
    url: `/riskcontrol/uic/oss/stop/word/list`,
    method: 'get',
    params
  })
}

/**
 * 禁用词新增和修改
 */
export function EditShopWord(data) {
  return request({
    url: `/riskcontrol/uic/oss/stop/word/edit`,
    method: 'post',
    data
  })
}

/**
 * 禁用词删除
 */
export function DeleteShopWord(data) {
  return request({
    url: `/riskcontrol/uic/oss/stop/word/delete`,
    method: 'post',
    data
  })
}

/**
 * 用户封禁 - 禁言历史记录
 */
export function GetCurrentLimitRec(params) {
  return request({
    url: `/uic/oss/member/currentLimitRec`,
    method: 'get',
    params
  })
}

/**
 * 永久忽略白名单列表
 */
export function GetWhiteList(params) {
  return request({
    url: `/riskcontrol/oss/whitelist/list`,
    method: 'get',
    params
  })
}

/**
 * 永久忽略白名单列表-更新缓存
 */
export function PostSyncCache(data) {
  return request({
    url: `/riskcontrol/oss/whitelist/syncCache`,
    method: 'post',
    data
  })
}

/**
 * 永久忽略白名单列表-移除
 */
export function PostRemove(data) {
  return request({
    url: `/riskcontrol/oss/whitelist/remove`,
    method: 'post',
    data
  })
}

/**
 * 禁用词新增和修改
 */
export function GetActionLimit(params) {
  return request({
    url: `/riskcontrol/oss/action/limit/list`,
    method: 'get',
    params
  })
}

/**
 * 禁用词删除
 */
export function PostRestoreLimit(data) {
  return request({
    url: `/riskcontrol/oss/action/limit/restoreLimit`,
    method: 'post',
    data
  })
}

/**
 * 联系车主记录
 */
export function GetRecordList(params) {
  return request({
    url: `/transaction/adminMotorUsedCarController/contact/record`,
    method: 'get',
    params
  })
}

/**
 * OSS用户头像审核列表
 */
export function GetAvatarList(params) {
  return request({
    url: `/forum/oss/user/info/check/avatar/list`,
    method: 'get',
    params
  })
}
/**
 * OSS用户头像批量审核
 */
export function PostAvatarBatchCheck(data) {
  return request({
    url: `/forum/oss/user/info/check/avatar/batchCheck`,
    method: 'post',
    data
  })
}

/**
 * OSS用户简介审核列表
 */
export function GetSignatureList(params) {
  return request({
    url: `/forum/oss/user/info/check/signature/list`,
    method: 'get',
    params
  })
}
/**
 * OSS用户简介批量审核
 */
export function PostSignatureBatchCheck(data) {
  return request({
    url: `/forum/oss/user/info/check/signature/batchCheck`,
    method: 'post',
    data
  })
}

/**
 * OSS用户钱包明细
 */
export function GetSerialsList(params) {
  return request({
    url: `/pirate/user/credit/oss/serials/list`,
    method: 'get',
    params
  })
}

/**
 * OSS用户钱包明细
 */
export function GetWithdrawList(params) {
  return request({
    url: `/pirate/user/credit/oss/wallet/withdraw/list`,
    method: 'get',
    params
  })
}

/**
 * 用户提现流水-操作_成功
 */
export function PostSuccessWithdraw(data) {
  return request({
    url: `/pirate/user/credit/oss/wallet/successWithdraw`,
    method: 'post',
    data
  })
}

/**
 * 用户提现流水-操作_失败
 */
export function PostFailWithDraw(data) {
  return request({
    url: `/pirate/user/credit/oss/wallet/failWithDraw`,
    method: 'post',
    data
  })
}

/**
 * 用户提现流水-操作_重试
 */
export function PostRetryWithdraw(data) {
  return request({
    url: `/pirate/user/credit/oss/wallet/retryWithdraw`,
    method: 'post',
    data
  })
}

/**
 * 敏感词库列表
 */
export function GetSensitiveStoreList(params) {
  return request({
    url: `/riskcontrol/oss/sensitive/store/list`,
    method: 'get',
    params
  })
}

/**
 * 新增敏感词库
 */
export function PostSensitiveStoreSave(data) {
  return request({
    url: `/riskcontrol/oss/sensitive/store/save`,
    method: 'post',
    data
  })
}

/**
 * 修改词库状态
 */
export function PostSensitiveStoreUpdate(data) {
  return request({
    url: `/riskcontrol/oss/sensitive/store/update`,
    method: 'post',
    data
  })
}

/**
 * 词库详情
 */
export function GetSensitiveStoreDetail(params) {
  return request({
    url: `/riskcontrol/oss/sensitive/store/detail`,
    method: 'get',
    params
  })
}

/**
 * 敏感词列表
 */
export function GetSensitiveWordList(params) {
  return request({
    url: `/riskcontrol/oss/sensitive/word/list`,
    method: 'get',
    params
  })
}

/**
 * 保存敏感词
 */
export function PostSensitiveWordSave(data) {
  return request({
    url: `/riskcontrol/oss/sensitive/word/save`,
    method: 'post',
    data
  })
}

/**
 * 修改敏感词状态
 */
export function PostSensitiveWordupdate(data) {
  return request({
    url: `/riskcontrol/oss/sensitive/word/updateStatus`,
    method: 'post',
    data
  })
}

/**
 * 生效
 */
export function PostSensitiveDataInit(data) {
  return request({
    url: `riskcontrol/oss/sensitive/data/init`,
    method: 'post',
    data
  })
}

/**
 * 解密身份证
 */
export function GetCommonDecryptIdcard(params) {
  return request({
    url: `/uic/oss/idcard/sensitive/commonDecryptIdcard`,
    method: 'get',
    params
  })
}

/*
 * 二手车受限用户检查
 */
export function RestrictedUserCheck(params) {
  return request({
    url: `/transactionmotor/UsedCarController/user/limit`,
    method: 'get',
    params
  })
}

/*
 * oss查看经销商微信号，手机号，身份证时，记录日志
 */
export function commonDecryptLog(params) {
  return request({
    url: `/uic/oss/idcard/sensitive/commonDecrypt/contentLog`,
    method: 'get',
    params
  })
}

/*
 * 获取当前直播数据-所有直播间
 */
export function getLiveCountryList(params) {
  return request({
    url: `${LIVEURL}/live/backend/v2/list`,
    method: 'get',
    params
  })
}

/*
 * 获取致命词列表
 */
export function getDeadWordList(params) {
  return request({
    url: `/riskcontrol/oss/fatal/word/list`,
    method: 'get',
    params
  })
}

/**
 * 保存敏感词
 */
export function addDeadWord(data) {
  return request({
    url: `/riskcontrol/oss/fatal/word/save`,
    method: 'post',
    data
  })
}
/**
 * 修改致命词状态
 */
export function updateDeadWordStatus(data) {
  return request({
    url: `/riskcontrol/oss/fatal/word/updateStatus`,
    method: 'post',
    data
  })
}
/**
 * 删除致命词
 */
export function deleteDeadWord(data) {
  return request({
    url: `/riskcontrol/oss/fatal/word/delete`,
    method: 'post',
    data
  })
}

/*
 * 致命词日志
 */
export function getDeadWordLogs(params) {
  return request({
    url: `/riskcontrol/oss/fatal/log`,
    method: 'get',
    params
  })
}

/*
 * 用户信息更改日志
 */
export function getQueryUserInfoChangeLog(params) {
  return request({
    url: `/user/center/oss/userInfo/queryUserInfoChangeLog`,
    method: 'get',
    params
  })
}

/*
 * 人员二手车审核手机，微信，身份证解密，并记录日志
 */
export function commonDecryptRecord(params) {
  return request({
    url: `/uic/oss/idcard/sensitive/commonDecrypt/senstive`,
    method: 'get',
    params
  })
}

/*
 * oss查询文章收益实名详情
 */
export function essayIncomeDetail(params) {
  return request({
    url: `/uic/oss/user/essay/income/detail`,
    method: 'get',
    params
  })
}

/*
 * 提现实名详情
 */
export function withdrawRealNameDetail(params) {
  return request({
    url: `/uic/oss/user/essay/income/withdrawRealNameDetail`,
    method: 'get',
    params
  })
}

/*
 * 职业数据
 */
export function getInfoProfession(params) {
  return request({
    url: `${APIURL}/uic/info/profession`,
    method: 'get',
    params
  })
}

/*
 * 获取认证信息
 */
export function getGetCertifyInfo(params) {
  return request({
    url: `/uic/oss/certApply/getCertifyInfo`,
    method: 'get',
    params
  })
}

/*
 * 私信对话列表
 */
export function privateMessagesList(params) {
  return request({
    url: `/uic/oss/pms/list`,
    method: 'get',
    params
  })
}

/*
 * 私信内容列表
 */
export function privateMessagesDetails(params) {
  return request({
    url: `/uic/oss/pms/details`,
    method: 'get',
    params
  })
}

/**
 * 新增认证
 */
export function addCertify(data) {
  return request({
    url: `/uic/oss/certApply/addCertify`,
    method: 'post',
    data
  })
}

/**
 * 编辑认证
 */
export function updateCertify(data) {
  return request({
    url: `/uic/oss/certApply/updateCertify`,
    method: 'post',
    data
  })
}

/*
 * 风险用户记录管理
 */
export function getHighRiskUserRecordList(params) {
  return request({
    url: `/forum/oss/highRiskUserController/getHighRiskUserRecordList`,
    method: 'get',
    params
  })
}

/*
 * 风险用户管理
 */
export function getHighRiskUserList(params) {
  return request({
    url: `/forum/oss/highRiskUserController/getHighRiskUserList`,
    method: 'get',
    params
  })
}

/*
 * 点击详情
 */
export function getHighRiskDetail(params) {
  return request({
    url: `/forum/oss/highRiskUserController/getHighRiskDetail`,
    method: 'get',
    params
  })
}

/*
 * 详情中文字、评论、头像、用户名、接口
 */
export function getHighRiskContent(params) {
  return request({
    url: `/forum/oss/highRiskUserController/getHighRiskContent`,
    method: 'get',
    params
  })
}

/*
 * 获取用户惩罚日志
 */
export function getUserPunishRecord(params) {
  return request({
    url: `/forum/oss/highRiskUserController/getUserPunishRecord`,
    method: 'get',
    params
  })
}

/**
 * 禁言-封号
 */
export function punishUserRecord(data) {
  return request({
    url: `/forum/oss/highRiskUserController/punishUserRecord`,
    method: 'post',
    data
  })
}
/**
 * 收钱吧退款记录
 */
export function GetRefundMoneyList(query) {
  return request({
    url: `/unite/order/oss/listRefund`,
    method: 'get',
    params: query
  })
}

/**
 * 收钱吧重新退款
 */
export function postRetryRefund(data) {
  return request({
    url: `/unite/order/oss/retryRefund`,
    method: 'post',
    data
  })
}
/**
 * 收钱吧退款完成
 */
export function postCompleteRefund(data) {
  return request({
    url: `/unite/order/oss/completeRefund`,
    method: 'post',
    data
  })
}

// 举报管理-二手车待处理列表
export function usedCarWaitingList(params) {
  return request({
    url: '/forum/oss/reportController/secondHandCar/reportOverview',
    method: 'get',
    params
  })
}
/**
 * 增长官任务列表
 */
export function getGorwTaskList(params) {
  return request({
    url: `/uic/oss/increase/task/list`,
    method: 'get',
    params
  })
}

// 二手车举报处理日志
export function usedCarWaitingLog(params) {
  return request({
    // url: '/forum/oss/reportController/handle/log',
    url: 'shop/oss/violation/punish/violation/record/list',
    method: 'get',
    params
  })
}
/**
 * 增长官任务详情
 */
export function getGorwTaskDetail(params) {
  return request({
    url: `/uic/oss/increase/task/detail`,
    method: 'get',
    params
  })
}
/**
 * 增长官任务 新增/修改活动
 */
export function postUpdateTask(data) {
  return request({
    url: `/uic/oss/increase/task/saveOrUpdate`,
    method: 'post',
    data
  })
}

/**
 * 增长官任务 更新任务状态
 */
export function postUpdateTaskStatus(data) {
  return request({
    url: `/uic/oss/increase/task/updateStatus`,
    method: 'post',
    data
  })
}

/**
 * 增长官用户任务列表
 */
export function getUserGorwTaskList(params) {
  return request({
    url: `/uic/oss/increase/task/audit/list`,
    method: 'get',
    params
  })
}

/**
 * 审核增长官任务
 */
export function postAuditGrowTask(data) {
  return request({
    url: `/uic/oss/increase/task/audit`,
    method: 'post',
    data
  })
}
// 根据用户ID获取简单用户信息
export function getSimpleInfoByUid(params) {
  return request({
    url: '/workOrder/oss/workOrder/userInfo/getSimpleInfoByUid',
    method: 'get',
    params
  })
}

/*
 * 获取举报详情
 */
export function getreportRecordDetail(params) {
  return request({
    url: `/forum/oss/reportController/reportDetail`,
    method: 'get',
    params
  })
}

/**
 * 发送邮箱验证码
 */
export function sendEmailVerifyCode(data) {
  return request({
    url: `/admin/auth/enterprise/account/sendEmailVerifyCode`,
    method: 'post',
    data
  })
}

/**
 * 修改密码
 */
export function modifyPassword(data) {
  return request({
    url: `/admin/auth/enterprise/account/modifyOssPassword`,
    method: 'post',
    data
  })
}

/*
 * 获取用户 是厂家还是商家 数据
 */
export function getUidIdentity(params) {
  return request({
    url: `/uic/oss/member/identity`,
    method: 'get',
    params
  })
}
/**
 * 获取实人认证日志
 */
export function getRealCheckLog(data) {
  return request({
    url: `/uic/oss/certApply/getRealCheckLog`,
    method: 'post',
    data
  })
}

/**
 * 批量导入用户名
 */
export function postBatNickName(data) {
  return request({
    url: `/audit/oss/change/batchHandleNameAuditFailList`,
    method: 'post',
    data
  })
}
/**
 * 获取创作者黑名单列表
 */
export function getAuthorBlacklist(params) {
  return request({
    url: `/forum/oss/blackAuthor/list`,
    method: 'get',
    params
  })
}

/**
 * 批量导入头像
 */
export function postBatAvatar(data) {
  return request({
    url: `/audit/oss/change/batchHandleAvatarAuditFailList`,
    method: 'post',
    data
  })
}

/**
 * 重试导入错误
 */
export function postBatRetry(data) {
  return request({
    url: `/audit/oss/change/batchHandleRecordFailList`,
    method: 'post',
    data
  })
}
/**
 * 查询审批导入
 */
export function getBatList(params) {
  return request({
    url: `/audit/oss/change/getChangeImportAuditList`,
    method: 'get',
    params
  })
}
/**
 * 保存黑名单
 */
export function postSaveBlackAuthor(data) {
  return request({
    url: `/forum/oss/blackAuthor/save`,
    method: 'POST',
    data
  })
}
/**
 * 移除黑名单
 */
export function postDeleteBlackAuthor(data) {
  return request({
    url: `/forum/oss/blackAuthor/deleteById`,
    method: 'POST',
    data
  })
}
/**
 * 举报分类统计
 */
export function getReportListCnt(params) {
  return request({
    url: `/forum/oss/reportController/reportListCnt`,
    method: 'get',
    params
  })
}
/**
 * 用户平台-举报管理页面批量处理举报接口
 */
export function postPageOneHandleReport(data) {
  return request({
    url: `/forum/oss/reportController/pageOneHandleReport`,
    method: 'POST',
    data
  })
}
/**
 * 举报历史
 */
export function getReportHistoryList(params) {
  return request({
    url: `/forum/oss/reportController/reportHistoryList`,
    method: 'get',
    params
  })
}
/**
 * 实人认证，业务来源
 */
export function getSourceTypeList(params) {
  return request({
    url: `/expands/oss/realUser/sourceType/list`,
    method: 'get',
    params
  })
}

/**
 * 获取举报评论上下文
 */
export function getCommentContent(params) {
  return request({
    url: `/reply/oss/auth/replyRelationList`,
    method: 'get',
    params
  })
}
/*
 * 移除黑名单
 */
export function getUserInfoTags(params) {
  return request({
    url: `/uic/oss/userInfo/tags`,
    method: 'get',
    params
  })
}

/**
 * 内容白名单列表
 */
export function getWhiteContentList(params) {
  return request({
    url: `/forum/oss/businessEssayController/getWhiteBusinessConfigList`,
    method: 'get',
    params
  })
}

/**
 * 保存白名单
 */
export function postSaveWhiteAuthor(data) {
  return request({
    url: `/forum/oss/businessEssayController/addWhiteBusinessConfig`,
    method: 'post',
    data
  })
}
/**
 * 移除白名单
 */
export function postDeleteWhiteAuthor(data) {
  return request({
    url: `/forum/oss/businessEssayController/delWhiteBusinessConfig`,
    method: 'post',
    data
  })
}

/**
 * 限制日志列表
 * @param params
 */
export function queryUserLimitedLog(params) {
  return request({
    url: '/riskcontrol/oss/action/limit/userLog',
    method: 'get',
    params
  })
}
/*
 * 私信重复记录
 */
export function getPrivacyRepeatList(params) {
  return request({
    url: `/uic/oss/pms/repeatRecord/list`,
    method: 'get',
    params
  })
}
/**
 * 私信重复相关用户
 */
export function getRepeatRecordUser(params) {
  return request({
    url: `/uic/oss/pms/repeatRecord/relatedUser`,
    method: 'get',
    params
  })
}
/*
 * 忘记密码-发送邮箱验证码
 * @param params
 */
export function getVerifyCodeByEmail(userCode) {
  return request({
    url: `/admin/auth/system/user/verifyCode/${userCode}`,
    method: 'get'
  })
}
/**
 * 忘记密码-重置密码
 */
export function resetPassword(data) {
  return request({
    url: `/admin/auth/system/user/forgetPassword.do`,
    method: 'post',
    data
  })
}
/**
 * 用户联系方式黑名单
 * @param params
 */
export function getUserBlacklist(params) {
  return request({
    url: '/uic/oss/blacklist/getByUid',
    method: 'get',
    params
  })
}

/**
 * 手机号关联查询
 * @param params
 */
export function getUserRecordlist(params) {
  return request({
    url: '/user/center/oss/userInfo/mobile/used/record',
    method: 'get',
    params
  })
}

/**
 * 保存、编辑帮助中心分类
 */
export function saveOrUpdateType(data) {
  return request({
    url: `/forum/oss/helpCenter/saveOrUpdateType`,
    method: 'post',
    data
  })
}

/**
 * 更新帮助中心分类状态
 */
export function updateTypeStatus(data) {
  return request({
    url: `/forum/oss/helpCenter/updateTypeStatus`,
    method: 'post',
    data
  })
}

/**
 * 更新分类排序值
 */
export function updateTypeSort(data) {
  return request({
    url: `/forum/oss/helpCenter/updateTypeSort`,
    method: 'post',
    data
  })
}

/**
 * 更新帮助中心问题排序值
 */
export function updateQuestionSort(data) {
  return request({
    url: `/forum/oss/helpCenter/updateQuestionSort`,
    method: 'post',
    data
  })
}

/**
 * 保存或编辑 问题
 */
export function saveOrUpdateIssue(data) {
  return request({
    url: '/forum/oss/helpCenter/saveOrUpdate',
    method: 'post',
    data
  })
}

/**
 * 更新帮助中心问题状态
 */
export function updateIssuetatus(data) {
  return request({
    url: `/forum/oss/helpCenter/updateStatus`,
    method: 'post',
    data
  })
}

/**
 * 问题分类列表
 */
export function getTypeList(params) {
  return request({
    url: '/forum/oss/helpCenter/listType',
    method: 'get',
    params
  })
}

/**
 * 根据typeId获取问题列表
 */
export function listQuestionByType(params) {
  return request({
    url: '/forum/oss/helpCenter/listQuestionByType',
    method: 'get',
    params
  })
}

/**
 * 根据id删除问题
 */
export function deleteById(data) {
  return request({
    url: `/forum/oss/helpCenter/deleteById`,
    method: 'post',
    data
  })
}
/**
 * 获取询价信息
 */
export function getInquireInfo(params) {
  return request({
    url: '/shop/oss/clue/customer/query/setclueCustomerDisturbFree',
    method: 'get',
    params
  })
}

/**
 * 用户是否存在二手车订单
 */
export function getUserExistOrder(params) {
  return request({
    url: '/transaction/adminMotorUsedCarOrderController/user/exist/order',
    method: 'get',
    params: {
      ...params,
      hideErrorMsg: true
    }
  })
}

/**
 * 骑行黑名单列表
 */
export function getTraceBlackList(params) {
  return request({
    url: '/carport/oss/traceBlack/list',
    method: 'get',
    params
  })
}

/**
 * 城市官约骑任务审核列表
 */
export function getCityTaskList(params) {
  return request({
    url: '/uic/oss/city/task/detail/list',
    method: 'get',
    params
  })
}

/**
 * 保存或编辑骑行黑名单
 */
export function traceBlackSaveOrUpdate(data) {
  return request({
    url: '/carport/oss/traceBlack/saveOrUpdate',
    method: 'post',
    data
  })
}

/**
 * 审核约骑城市官任务
 */
export function postCityTaskAudit(data) {
  return request({
    url: `/uic/oss/city/task/detail/audit`,
    method: 'post',
    data
  })
}

/**
 * 删除骑行黑名单
 */
export function traceBlackDeleteById(data) {
  return request({
    url: '/carport/oss/traceBlack/deleteById',
    method: 'post',
    data
  })
}

/**
 * 骑行周榜
 */
export function getWeekRank(params) {
  return request({
    url: '/uic/oss/trace/week/rank',
    method: 'get',
    params
  })
}

/**
 * 点评官监控
 */
export function getCommentorMonlist(params) {
  return request({
    url: '/reply/oss/officerMonitorController/list',
    method: 'get',
    params
  })
}

/**
 * 添加能量夺宝
 */
export function postAddSnatch(data) {
  return request({
    url: '/mall/oss/snatch/add',
    method: 'post',
    data
  })
}

/**
 * 能量夺宝列表
 */
export function getSnatchList(params) {
  return request({
    url: '/mall/oss/snatch/list',
    method: 'get',
    params
  })
}

/**
 * 能量夺宝无效活动
 */
export function postUpdateValid(data) {
  return request({
    url: '/mall/oss/snatch/updateValid',
    method: 'post',
    data
  })
}

/**
 * 能量夺宝编辑
 */
export function postSnatchEdit(data) {
  return request({
    url: '/mall/oss/snatch/edit',
    method: 'post',
    data
  })
}

/**
 * 特聘官开关列表
 */
export function listApplyOnOffVO(params) {
  return request({
    url: '/uic/oss/growth/officer/listApplyOnOffVO',
    method: 'get',
    params
  })
}

/**
 * 更新特聘官身份申请开关
 */
export function updateApplyStatus(data) {
  return request({
    url: '/uic/oss/growth/officer/updateApplyStatus',
    method: 'post',
    data
  })
}

//能量抽奖 中奖号码
export function postConfirmNum(data) {
  return request({
    url: '/mall/oss/snatch/firstCommitLuckyNumber',
    method: 'post',
    data
  })
}

//能量抽奖 休市期维护
export function postBreakConfirm(data) {
  return request({
    url: '/mall/oss/snatch/addClosePeriodRecord',
    method: 'post',
    data
  })
}

//能量抽奖 休市期维护详情
export function getBreakDetail(params) {
  return request({
    url: '/mall/oss/snatch/queryClosePeriodRecord',
    method: 'get',
    params
  })
}

/**
 * 城市官开放城市
 */
export function saveOrUpdateApplyPlace(data) {
  return request({
    url: '/uic/oss/growth/officer/saveOrUpdateApplyPlace',
    method: 'post',
    data
  })
}

/**
 * 能量抽奖 中奖号码详情
 */
export function queryLuckyNumberInfo(params) {
  return request({
    url: '/mall/oss/snatch/queryLuckyNumberInfo',
    method: 'get',
    params
  })
}

/**
 *  城市官开放城市列表
 */
export function getListApplyPlace(params) {
  return request({
    url: '/uic/oss/growth/officer/listApplyPlace',
    method: 'get',
    params
  })
}

//能量抽奖 中奖号码
export function finalCommitLuckyNumber(data) {
  return request({
    url: '/mall/oss/snatch/finalCommitLuckyNumber',
    method: 'post',
    data
  })
}
//能量抽奖 中奖预览
export function getPreviewWinUser(params) {
  return request({
    url: '/mall/oss/snatch/previewWinUser',
    method: 'get',
    params
  })
}

//查询特聘官信息
export function getOfficerInfo(params) {
  return request({
    url: '/uic/oss/userInfo/officerInfo',
    method: 'get',
    params
  })
}

//群聊列表
export function getGroupMessageList(params) {
  return request({
    url: '/uic/oss/group/chat/list',
    method: 'get',
    params
  })
}

//中奖人员列表
export function getWinUserList(params) {
  return request({
    url: '/mall/oss/snatch/winUserList',
    method: 'get',
    params
  })
}
//新增转盘活动
export function postSaveOrUpdate(data) {
  return request({
    url: '/coins/oss/turntable/activity/saveOrUpdate',
    method: 'post',
    data
  })
}

//更新转盘活动状态
export function postUpdateTurntableStatus(data) {
  return request({
    url: '/coins/oss/turntable/activity/updateStatus',
    method: 'post',
    data
  })
}

//转盘活动活动列表
export function getTurntableList(params) {
  return request({
    url: '/coins/oss/turntable/activity/list',
    method: 'get',
    params
  })
}

//转盘活动活动活动详情
export function getTurntableDetail(params) {
  return request({
    url: '/coins/oss/turntable/activity/detail',
    method: 'get',
    params
  })
}

//奖品中奖情况
export function getTurntableAwardData(params) {
  return request({
    url: '/coins/oss/turntable/awardData/list',
    method: 'get',
    params
  })
}

//用户中奖情况
export function getTurntableUserList(params) {
  return request({
    url: '/coins/oss/turntable/user/list',
    method: 'get',
    params
  })
}

//获取京东卡数量
export function getRewardCardCountByState(params) {
  return request({
    url: '/pirate/user/credit/oss/rewardCard/countByState',
    method: 'get',
    params
  })
}
//内容大咖专栏新增、编辑
export function postColumnSaveOrUpdate(data) {
  return request({
    url: '/forum/oss/specialColumnController/saveOrUpdate',
    method: 'post',
    data
  })
}

//专栏列表
export function getColumnList(params) {
  return request({
    url: '/forum/oss/specialColumnController/list',
    method: 'get',
    params
  })
}

//专栏详情
export function getColumnDetail(params) {
  return request({
    url: '/forum/oss/specialColumnController/detail',
    method: 'get',
    params
  })
}

//内容大咖专栏新增、编辑
export function postColumnDeleteById(data) {
  return request({
    url: '/forum/oss/specialColumnController/deleteById',
    method: 'post',
    data
  })
}

//内容大咖专栏新增、编辑
export function postColumnSort(data) {
  return request({
    url: '/forum/oss/specialColumnController/sort',
    method: 'post',
    data
  })
}

//点评官监控数据详情
export function getOfficerMonitorInfo(params) {
  return request({
    url: '/reply/oss/officerMonitorController/detail',
    method: 'get',
    params
  })
}

//点评官监控数据提醒
export function postSendMessage(data) {
  return request({
    url: '/reply/oss/officerMonitorController/sendMessage',
    method: 'post',
    data
  })
}

//点评官监控数据提醒
export function getOfficerMonitorControllerList(params) {
  return request({
    url: '/reply/oss/officerMonitorController/user/list',
    method: 'get',
    params
  })
}

//对点评官操作的记录
export function getOfficerMonitorControllerLogList(params) {
  return request({
    url: '/reply/oss/officerMonitorController/log/list',
    method: 'get',
    params
  })
}

/**
 * 保存搬运账号
 */
export function postSaveCarryAccount(data) {
  return request({
    url: `/uic/oss/accountType/carryAccount/add`,
    method: 'POST',
    data
  })
}
/**
 * 移除搬运账号
 */
export function postDeleteCarryAccount(data) {
  return request({
    url: `/uic/oss/accountType/carryAccount/delete`,
    method: 'POST',
    data
  })
}

/**
 * 获取搬运账号列表
 */
export function getCarryAccountlist(params) {
  return request({
    url: `/uic/oss/accountType/carryAccount/List`,
    method: 'get',
    params
  })
}

/**
 * 生成短链
 */
export function postCreateshortlink(params) {
  return request({
    url: `/expands/oss/shortlink/create`,
    method: 'get',
    params
  })
}

/**
 * 短链列表
 */
export function getShortlinkList(params) {
  return request({
    url: `/expands/oss/shortlink/list`,
    method: 'get',
    params
  })
}

/**
 * 点击链接事件查询
 */
export function getShortlinkStatistics(params) {
  return request({
    url: `/expands/oss/shortlink/statistics`,
    method: 'get',
    params
  })
}

/**
 * 内容大咖更新状态
 */
export function postCreatorColumnUpdateStatus(data) {
  return request({
    url: `/forum/oss/specialColumnController/updateStatus`,
    method: 'POST',
    data
  })
}
