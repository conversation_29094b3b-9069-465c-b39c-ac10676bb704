import request from '@/utils/request'
/**
 * 用户认证查询
 * @param data
 */
export function userApprove(data) {
  return request({
    url: '/uic/oss/accountType/list',
    method: 'post',
    params: data
  })
}
/**
 * 用户认证保存/更新
 * @param data
 */
export function saveAndDeletePrevious(data) {
  return request({
    url: '/uic/oss/accountType/saveAndDeletePrevious',
    method: 'post',
    params: data
  })
}

/**
 * 申请列表查询
 * @param data
 */
export function searchCertApply(data) {
  return request({
    url: '/uic/oss/certApply/listApply',
    method: 'get',
    params: data
  })
}

/**
 * 申请详情
 * @param data
 */
export function getCertApplyDetail(data) {
  return request({
    url: '/uic/oss/certApply/selectById',
    method: 'get',
    params: data
  })
}

/**
 * 更新申请状态
 * @param data
 */
export function updateCertApplyStatus(data) {
  return request({
    url: '/uic/oss/certApply/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

/**
 * 批量编辑用户认证信息
 * @param data
 */
export function batchSaveOrUpdate(data) {
  return request({
    url: '/uic/oss/accountType/batchSaveOrUpdate',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10301',
    data
  })
}

/**
 * 微信解绑
 * @param data
 */
export function removeWechat(data) {
  return request({
    url: '/uic/oss/member/removeWechat',
    method: 'post',
    beforeAlterFirst: false,
    menu: 'S10301',
    data
  })
}

/**
 * 安全官列表
 * @param data
 */
export function getGuardList(data) {
  return request({
    url: '/uic/oss/security/guard/list',
    method: 'get',
    params: data
  })
}

/**
 * 安全官-新增
 * @param data
 */
export function addGuard(data) {
  return request({
    url: '/uic/oss/security/guard/add',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

/**
 * 安全官-安全官开通状态
 * @param data
 */
export function updateStatusGuard(data) {
  return request({
    url: '/uic/oss/security/guard/updateStatus',
    method: 'post',
    beforeAlterFirst: false,
    data
  })
}

/**
 * 城市官审核1=通过2=不通过3=取消
 * @param data
 */
export function cityUnAgree(data) {
  return request({
    url: '/uic/oss/city/officer/audit',
    method: 'post',
    data
  })
}

/**
 * 城市官审核列表
 * @param data
 */
export function getCityAuthList(data) {
  return request({
    url: '/uic/oss/city/officer/listApply',
    method: 'get',
    params: data
  })
}

/**
 * 城市官管理列表
 * @param data
 */
export function getCityAuthManageList(data) {
  return request({
    url: '/uic/oss/city/officer/list',
    method: 'get',
    params: data
  })
}

/**
 * 新增城市官
 * @param data
 */
export function createCityOrder(data) {
  return request({
    url: '/uic/oss/city/officer/saveCityOfficer',
    method: 'post',
    data
  })
}

/**
 * 用户绑定品牌
 * @param data
 */
export function accountBrandBind(data) {
  return request({
    url: '/factory/oss/account/brand/bind',
    method: 'post',
    data
  })
}

/**
 * 用户已绑定品牌列表
 * @param data
 */
export function accountGetBrandsByUid(query) {
  return request({
    url: '/factory/oss/account/brand/getBrandsByUid',
    method: 'get',
    params: query
  })
}

/**
 * 增长官列表
 * @param query
 */
export function growthOfficerList(query) {
  return request({
    url: '/uic/oss/growth/officer/list',
    method: 'get',
    params: query
  })
}

/**
 * 新增增长官
 * @param data
 */
export function growthOfficerAdd(data) {
  return request({
    url: '/uic/oss/growth/officer/add',
    method: 'post',
    data
  })
}

/**
 *增长官增加备注
 * @param data
 */
export function growthOfficerAddRemark(data) {
  return request({
    url: '/uic/oss/growth/officer/addRemark',
    method: 'post',
    data
  })
}

/**
 * 修改增长官开通状态
 * @param data
 */
export function growthOfficerUpdateStatus(data) {
  return request({
    url: '/uic/oss/growth/officer/updateStatus',
    method: 'post',
    data
  })
}
//增长官审核列表
export function growthOfficerAuditList(query) {
  return request({
    url: '/uic/oss/growth/officer/listApply',
    method: 'get',
    params: query
  })
}
//增长官操作记录
export function growthOfficerOperateList(query) {
  return request({
    url: '/uic/oss/growth/officer/operate/log',
    method: 'post',
    data: query
  })
}

//增长官任务统计数据
export function growthTaskRecord(params) {
  return request({
    url: '/uic/oss/increase/task/taskRecord',
    method: 'get',
    params
  })
}

//增长官审核
export function growthOfficerAuditData(data) {
  return request({
    url: '/uic/oss/growth/officer/audit',
    method: 'post',
    data
  })
}

// 重置用户名
export function UpdateResetUserName(data) {
  return request({
    url: '/user/center/oss/userInfo/resetUserName',
    method: 'post',
    data
  })
}

// 重置用户头像
export function UpdateResetUserAvatar(data) {
  return request({
    url: '/user/center/oss/userInfo/resetUserAvatar',
    method: 'post',
    data
  })
}

/**
 * 实名认证列表接口
 * @param data
 */
export function getListRealApply(data) {
  return request({
    url: '/uic/oss/certApply/listRealApply',
    method: 'get',
    params: data
  })
}
//巡查官审核列表
export function patrolOfficerAuditList(query) {
  return request({
    url: '/uic/oss/police/officer/listApply',
    method: 'get',
    params: query
  })
}
//增长官审核
export function patrolOfficerAuditData(data) {
  return request({
    url: '/uic/oss/police/officer/audit',
    method: 'post',
    data
  })
}
/**
 * 巡查官列表
 * @param query
 */
export function patrolOfficerList(query) {
  return request({
    url: '/uic/oss/police/officer/list',
    method: 'get',
    params: query
  })
}
/**
 * 修改巡查官开通状态
 * @param data
 */
export function patrolOfficerUpdateStatus(data) {
  return request({
    url: '/uic/oss/police/officer/updateStatus',
    method: 'post',
    data
  })
}
//增长官操作记录
export function patrolOfficerOperateList(query) {
  return request({
    url: '/uic/oss/police/officer/operate/log',
    method: 'post',
    data: query
  })
}

/**
 * 巡查数据源列表
 * @param query
 */
export function informationList(query) {
  return request({
    url: '/transaction/check/car/list/information',
    method: 'get',
    params: query
  })
}

// 保存巡查学院内容配置
export function informationSave(query) {
  return request({
    url: '/transaction/check/car/save/information',
    method: 'post',
    data: query
  })
}

// 修改巡查学院内容配置
export function informationUpdate(query) {
  return request({
    url: '/transaction/check/car/update/information',
    method: 'post',
    data: query
  })
}

// 巡查学院删除
export function informationDelete(query) {
  return request({
    url: '/transaction/check/car/delete/information',
    method: 'post',
    data: query
  })
}

// 巡查学院排序
export function informationSort(query) {
  return request({
    url: '/transaction/check/car/sort/information',
    method: 'post',
    data: query
  })
}

// 巡查学院内容日志
export function informationLog(query) {
  return request({
    url: '/transaction/check/car/log/information',
    method: 'get',
    params: query
  })
}

/**
 * 邀请巡查官列表
 * @param query
 */
export function getListInvite(query) {
  return request({
    url: '/uic/oss/invite/officer/listInvite',
    method: 'get',
    params: query
  })
}

// 导入可申请巡查官
export function saveInviteOfficer(query) {
  return request({
    url: '/uic/oss/invite/officer/saveInviteOfficer',
    method: 'post',
    data: query
  })
}

/**
 * 巡查车型数据配置列表
 * @param query
 */
export function getSourceList(query) {
  return request({
    url: '/transaction/check/car/data/source/list',
    method: 'get',
    params: query
  })
}

/**
 *  巡查车型数据配置列表日志
 * @param query
 */
export function getSourceLog(query) {
  return request({
    url: '/transaction/check/car/data/source/log',
    method: 'get',
    params: query
  })
}

// 巡查车型数据配置列表-配置
export function saveInviteSource(query) {
  return request({
    url: '/transaction/check/car/update/data/source',
    method: 'post',
    data: query
  })
}

/**
 * 点评官列表
 * @param query
 */
export function commentorList(query) {
  return request({
    url: '/uic/oss/comment/officer/list',
    method: 'get',
    params: query
  })
}

/**
 * 新增点评官
 * @param data
 */
export function commentorAdd(data) {
  return request({
    url: '/uic/oss/comment/officer/add',
    method: 'post',
    data
  })
}

/**
 *点评官增加备注
 * @param data
 */
export function commentorAddRemark(data) {
  return request({
    url: '/uic/oss/comment/officer/addRemark',
    method: 'post',
    data
  })
}

/**
 * 修改点评官开通状态
 * @param data
 */
export function commentorUpdateStatus(data) {
  return request({
    url: '/uic/oss/comment/officer/updateStatus',
    method: 'post',
    data
  })
}
//点评官申请列表
export function commentorAuditList(query) {
  return request({
    url: '/uic/oss/comment/officer/listApply',
    method: 'get',
    params: query
  })
}
//点评官操作记录
export function commentorOperateList(params) {
  return request({
    url: '/uic/oss/comment/officer/operate/log',
    method: 'get',
    params
  })
}

//点评官审核
export function commentorAuditData(data) {
  return request({
    url: '/uic/oss/comment/officer/audit',
    method: 'post',
    data
  })
}

//点评官数据
export function getCommentorData(params) {
  return request({
    url: '/reply/oss/replyController/officialData',
    method: 'get',
    params
  })
}
/**
 * 修改城市官开通状态
 * @param data
 */
export function cityUpdateStatus(data) {
  return request({
    url: '/uic/oss/city/officer/updateStatus',
    method: 'post',
    data
  })
}

// 任务说明列表
export function checkTaskConfigList(params) {
  return request({
    url: '/transaction/oss/check/task/config/list',
    method: 'get',
    params
  })
}

// 编辑说明
export function checkTaskConfigEdit(data) {
  return request({
    url: '/transaction/oss/check/task/config/edit',
    method: 'post',
    data
  })
}

// 添加说明
export function checkTaskConfigSave(data) {
  return request({
    url: '/transaction/oss/check/task/config/save',
    method: 'post',
    data
  })
}

/**
 * 邀请城市官列表
 * @param query
 */
export function getListCityOfficerConfig(query) {
  return request({
    url: '/uic/oss/city/officer/listCityOfficerConfig',
    method: 'get',
    params: query
  })
}

// 导入可申请城市官
export function saveCityOfficerConfig(query) {
  return request({
    url: '/uic/oss/city/officer/saveCityOfficerConfig',
    method: 'post',
    data: query
  })
}

/**
 * 用户连续签到天数 及 总签到天数
 * @param query
 */
export function getContinueAndTotalSignDays(query) {
  return request({
    url: '/coins/oss/signLog/getContinueAndTotalSignDays',
    method: 'get',
    params: query
  })
}

/**
 * 用户在指定年月的签到记录
 * @param query
 */
export function getSignRecordByYearMonth(query) {
  return request({
    url: '/coins/oss/signLog/getSignRecordByYearMonth',
    method: 'get',
    params: query
  })
}
