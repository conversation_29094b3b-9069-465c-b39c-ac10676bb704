import request from '@/utils/request'

// 二手车处罚列表
export function getPublishList(data) {
  return request({
    // url: '/carport/oss/punish/list',
    url: '/shop/oss/violation/punish/all/punish/list',
    method: 'get',
    params: data
  })
}
// 二手车处罚列表-待处罚
export function getPublishListAwait(data) {
  return request({
    url: 'shop/oss/violation/punish/notHandle/punish/list',
    method: 'get',
    params: data
  })
}
// 获取处罚信息
export function getPunishInfo(data) {
  return request({
    url: '/carport/oss/punish/getPunishInfo',
    method: 'get',
    params: data
  })
}
// 释放处罚
export function releasePunish(data) {
  return request({
    // url: '/carport/oss/punish/releasePunish',
    url: '/shop/oss/violation/punish/release/punish',
    method: 'post',
    data
  })
}
// 查询处罚日志
export function getPunishLogs(data) {
  return request({
    // url: '/carport/oss/punish/log',
    url: '/shop/oss/violation/punish/all/record/list',
    method: 'get',
    params: data
  })
}
// 获取用户现有处罚信息
export function getUserPunishInfo(data) {
  return request({
    // url: '/carport/oss/punish/getExistPunish',
    url: '/shop/oss/violation/punish/shop/info',
    method: 'get',
    params: data
  })
}
// 获取用户90天处罚数
export function userPenaltyNum(data) {
  return request({
    url: '/shop/oss/violation/punish/punish/count',
    method: 'get',
    params: data
  })
}
// 获取用户现有处罚信息
export function userPenaltyInfo(data) {
  return request({
    url: 'shop/oss/violation/punish/punish/info',
    method: 'get',
    params: data
  })
}
// 黑名单列表页
export function getUserBlackList(data) {
  return request({
    url: '/transaction/oss/black/list',
    method: 'get',
    params: data
  })
}
// 获取用户处罚记录列表
export function getUserPunishRecords(data) {
  return request({
    // url: '/carport/oss/punish/record/list',
    url: '/shop/oss/violation/punish/punish/record/list',
    method: 'get',
    params: data
  })
}
// 二手车举报列表
export function usedCarReportList(data) {
  return request({
    url: '/forum/oss/reportController/secondHandCar/reportList',
    method: 'get',
    params: data
  })
}
// 二手车举报详情
export function usedCarReportDetail(data) {
  return request({
    url: '/forum/oss/reportController//secondHandCar/reportDetail',
    method: 'get',
    params: data
  })
}
// 有效性修改
export function upateEffective(data) {
  return request({
    url: '/transaction/oss/black/update/effective',
    method: 'post',
    data
  })
}
// 撤销处罚
export function undoPunish(data) {
  return request({
    // url: '/carport/oss/punish/withdraw/punish',
    url: '/shop/oss/violation/punish/withdraw/punish',
    method: 'post',
    data
  })
}
// 添加黑名单手机号
export function addBlackListMobile(data) {
  return request({
    url: '/transaction/oss/black/add/mobile',
    method: 'post',
    data
  })
}
// 批量添加黑名单
export function batchAddBlackListMobile(data) {
  return request({
    url: '/transaction/oss/black/batch/add/account',
    method: 'post',
    data
  })
}
// 关联手机号用户
export function getRelatedAccount(data) {
  return request({
    // url: '/transaction/oss/black/related/account',
    url: '/transaction/oss/black/related/user',
    method: 'get',
    params: data
  })
}
// 二手车联系方式黑名单 日志
export function getRelatedLog(data) {
  return request({
    url: '/transaction/oss/black/operate/log',
    method: 'get',
    params: data
  })
}
// 历史日志记录
export function getRiskManagerLog(data) {
  return request({
    url: '/uic/oss/risk/manager/log',
    method: 'get',
    params: data
  })
}
// 用户风险管理列表
export function getRiskManagerList(data) {
  return request({
    url: '/uic/oss/risk/manager/list',
    method: 'get',
    params: data
  })
}
// 新增风险用户
export function riskManagerAdd(data) {
  return request({
    url: '/uic/oss/risk/manager/add',
    method: 'post',
    data
  })
}
// 批量添加风险用户
export function riskManagerBatchAdd(data) {
  return request({
    url: '/uic/oss/risk/manager/batch/add',
    method: 'post',
    data
  })
}
// 打开风险弹框
export function riskManagerOpenRiskTip(data) {
  return request({
    url: '/uic/oss/risk/manager/open/riskTip',
    method: 'post',
    data
  })
}
// 禁止查看手机微信
export function riskManagerOpenLimitContact(data) {
  return request({
    url: '/uic/oss/risk/manager/open/limitContact',
    method: 'post',
    data
  })
}
// 限制私信
export function riskManagerOpenLimitPm(data) {
  return request({
    url: '/uic/oss/risk/manager/open/limitPm',
    method: 'post',
    data
  })
}
// 打开风险弹框(手机号)
export function riskManagerOpenRiskTipMoblie(data) {
  return request({
    url: '/uic/oss/risk/manager/open/riskTipByMobile',
    method: 'post',
    data
  })
}
// 禁止查看手机微信(手机号)
export function riskManagerOpenLimitContactMobile(data) {
  return request({
    url: '/uic/oss/risk/manager/open/limitContactByMobile',
    method: 'post',
    data
  })
}
// 限制私信(手机号)
export function riskManagerOpenLimitPmByMobile(data) {
  return request({
    url: '/uic/oss/risk/manager/open/limitPmByMobile',
    method: 'post',
    data
  })
}
// 二手车发布()
export function riskManagerOpenLimitUsedCar(data) {
  return request({
    url: '/uic/oss/risk/manager/open/limitPublish',
    method: 'post',
    data
  })
}
// 二手车发布(手机号)
export function riskManagerOpenLimitUsedCarMobile(data) {
  return request({
    url: '/uic/oss/risk/manager/open/limitPublishByMobile',
    method: 'post',
    data
  })
}

// 二手车举报处理配置-配置列表
export function getConfigList(data) {
  return request({
    url: '/transaction/oss/report/reason/list',
    method: 'get',
    params: data
  })
}
// 二手车举报处理配置-新增配置
export function addNewProfile(data) {
  return request({
    url: '/transaction/oss/report/reason/create',
    method: 'post',
    data
  })
}
// 二手车举报处理配置-编辑配置
export function editConfig(data) {
  return request({
    url: '/transaction/oss/report/reason/edit',
    method: 'post',
    data
  })
}
// 二手车举报处理配置-配置详情
export function getConfigDetails(data) {
  return request({
    url: '/transaction/oss/report/reason/detail',
    method: 'get',
    params: data
  })
}
// 二手车举报处理配置-配置删除
export function deleteProfile(data) {
  return request({
    url: '/transaction/oss/report/reason/delete',
    method: 'post',
    data
  })
}
// oss二手车相似图片列表
export function similarImagesList(data) {
  return request({
    url: '/transaction/oss/similar/images/list',
    method: 'get',
    params: data
  })
}
// oss异常图片监控
export function updateFollowStatus(data) {
  return request({
    url: '/transaction/oss/similar/images/updateFollowStatus',
    method: 'post',
    data
  })
}

// 二手车审核不通过原因配置-原因列表
export function usedCarsCauseList(data) {
  return request({
    url: '/expands/oss/common/reason/query/all',
    method: 'get',
    params: data
  })
}

// 二手车审核不通过原因配置-保存原因分类
export function usedCarsCauseSaveType(data) {
  return request({
    url: '/expands/oss/common/reason/save/type',
    method: 'post',
    data
  })
}

// 二手车审核不通过原因配置-保存原因详情
export function usedCarsCauseSaveDetails(data) {
  return request({
    url: '/expands/oss/common/reason/full/save',
    method: 'post',
    data
  })
}

// 二手车审核不通过原因配置-删除
export function usedCarsCauseDeleteType(data) {
  return request({
    url: '/expands/oss/common/reason/delete/type',
    method: 'post',
    data
  })
}

// 查询日志
export function similarImagesOperateLog(params) {
  return request({
    url: '/transaction/oss/similar/images/operate/log',
    method: 'get',
    params
  })
}
// 插入日志
export function similarImagesSaveLog(data) {
  return request({
    url: '/transaction/oss/similar/images/save/log',
    method: 'post',
    data
  })
}

// oss二手车相似车牌列表
export function similarLicensePlateList(data) {
  return request({
    url: '/transaction/oss/similar/license/plate/list',
    method: 'get',
    params: data
  })
}

// 车牌插入日志
export function similarLicensePlateSaveLog(data) {
  return request({
    url: '/transaction/oss/similar/license/plate/save/log',
    method: 'post',
    data
  })
}

// 车牌查询日志
export function similarLicensePlateOperateLog(data) {
  return request({
    url: '/transaction/oss/similar/license/plate/operate/log',
    method: 'get',
    params: data
  })
}
// oss异常车牌更新跟进状态
export function licensePlateUpdateFollowStatus(data) {
  return request({
    url: '/transaction/oss/similar/license/plate/updateFollowStatus',
    method: 'post',
    data
  })
}

// 新增标记或违规
export function createMarkedViolation(data) {
  return request({
    url: '/shop/oss/violation/punish/create/marked/violation',
    method: 'post',
    data
  })
}

// 标记违规列表
export function markedViolation(data) {
  return request({
    url: '/shop/oss/violation/punish/marked/violation',
    method: 'get',
    params: data
  })
}

// 释放标记或违规
export function releaseMarkedViolation(data) {
  return request({
    url: '/shop/oss/violation/punish/release/marked/violation',
    method: 'post',
    data
  })
}

// 用户车牌受限列表
export function getAdminMotorSecondHandCarUserLicenseController(query) {
  return request({
    url: '/transaction/adminMotorSecondHandCarUserLicenseController/list',
    method: 'get',
    params: query
  })
}

// 释放车牌
export function releaseAdminMotorSecondHandCarUserLicenseController(data) {
  return request({
    url: '/transaction/adminMotorSecondHandCarUserLicenseController/release/License',
    method: 'post',
    data
  })
}

// 用户车牌受限列表-日志
export function getAdminMotorSecondHandCarUserLicenseControllerLog(query) {
  return request({
    url: '/transaction/adminMotorSecondHandCarUserLicenseController/operate/log',
    method: 'get',
    params: query
  })
}

// 摩宁订单列表
export function getOrderList(query) {
  return request({
    url: '/transaction/adminMotorMnCarController/order/list',
    method: 'get',
    params: query
  })
}

// 摩宁订单列表价格汇总
export function getTotalAmount(query) {
  return request({
    url: '/transaction/adminMotorMnCarController/order/totalAmount',
    method: 'get',
    params: query
  })
}

// 摩宁订单同步订单
export function postTotalAmount(data) {
  return request({
    url: '/transaction/adminMotorMnCarController/order/syncOrder',
    method: 'post',
    data
  })
}

// 二手车审核提示列表
export function auditTipsList(params) {
  return request({
    url: '/transaction/admin/audit/tips/list',
    method: 'get',
    params
  })
}

// 二手车审核提示新增or编辑
export function auditTipsSaveOrUpdate(data) {
  return request({
    url: '/transaction/admin/audit/tips/auditTips/saveOrUpdate',
    method: 'post',
    data
  })
}

// 二手车审核提示删除
export function auditTipsDelete(data) {
  return request({
    url: '/transaction/admin/audit/tips/delete',
    method: 'post',
    data
  })
}

// 新增原因
export function reasonSaveDetail(data) {
  return request({
    url: '/expands/oss/common/reason/save/detail',
    method: 'post',
    data
  })
}

// 新增原因
export function reasonOrderDetail(data) {
  return request({
    url: '/expands/oss/common/reason/order/detail',
    method: 'post',
    data
  })
}

// 关键词分页
export function highlightKeyword(params) {
  return request({
    url: '/transaction/oss/highlightKeyword/getPage',
    method: 'get',
    params
  })
}

// 分类列表
export function highlightKeywordType(params) {
  return request({
    url: '/transaction/oss/highlightKeywordType/getList',
    method: 'get',
    params
  })
}

// 新增关键词
export function highlightKeywordAdd(data) {
  return request({
    url: '/transaction/oss/highlightKeyword/addList',
    method: 'post',
    data
  })
}

// 新增关键词_导入
export function highlightKeywordImport(data) {
  return request({
    url: '/transaction/oss/highlightKeyword/importKeyword',
    method: 'post',
    contentType: false,
    data,
    transformRequest(data) {
      return JSON.stringify(data)
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 新增分类
export function highlightKeywordAddType(data) {
  return request({
    url: '/transaction/oss/highlightKeywordType/add',
    method: 'post',
    data
  })
}

// 新增分类
export function highlightKeywordDelete(data) {
  return request({
    url: '/transaction/oss/highlightKeyword/delete',
    method: 'post',
    data
  })
}

// 二手车黑名单（注销反复注册） 列表
export function getDeregistrationBlacklist(params) {
  return request({
    url: '/transaction/black/logout/user/list',
    method: 'get',
    params
  })
}

// 二手车黑名单（注销反复注册） 修改
export function deregistrationBlacklistRevise(data) {
  return request({
    url: '/transaction/black/logout/user/effective',
    method: 'post',
    data
  })
}

// 二手车黑名单（注销反复注册） 日志
export function deregistrationBlacklistLog(params) {
  return request({
    url: '/transaction/black/logout/user/query/log',
    method: 'get',
    params
  })
}

// 举报自定义消息内容-列表
export function getContentListByIdtype(params) {
  return request({
    url: '/forum/oss/reportController/getContentListByIdtype',
    method: 'get',
    params
  })
}

// 举报自定义消息内容-新增/修改
export function saveCustomContent(data) {
  return request({
    url: '/forum/oss/reportController/saveCustomContent',
    method: 'post',
    data
  })
}

// 举报自定义消息内容-删除
export function deleteCustomContent(data) {
  return request({
    url: '/forum/oss/reportController/deleteCustomContent',
    method: 'post',
    data
  })
}

// 二手车处罚列表-纠纷记录
export function getDisputeListAwait(data) {
  return request({
    url: 'shop/oss/violation/punish/dispute/list',
    method: 'get',
    params: data
  })
}

// 二手车处罚列表-新增纠纷记录
export function postDisputeData(data) {
  return request({
    url: 'shop/oss/violation/punish/create/dispute',
    method: 'post',
    data
  })
}

// 二手车处罚列表-删除纠纷记录
export function deleteDisputeData(data) {
  return request({
    url: 'shop/oss/violation/punish/dispute/remove',
    method: 'post',
    data
  })
}

// 获取商家关联信息
export function queryMerchantReportRel(params) {
  return request({
    url: '/forum/oss/reportController/queryMerchantReportRel',
    method: 'get',
    params
  })
}

// 保存经销商关联数据
export function saveMerchantReportRel(data) {
  return request({
    url: '/forum/oss/reportController/saveMerchantReportRel',
    method: 'post',
    data
  })
}

// 删除商家关联信息
export function deleteMerchantReportRel(data) {
  return request({
    url: '/forum/oss/reportController/deleteMerchantReportRel',
    method: 'post',
    data
  })
}

// 处罚记录
export function getCrimeSheet(params) {
  return request({
    url: '/shop/oss/violation/punish/punish/record',
    method: 'get',
    params
  })
}

// 二手车私信词库黑名单_分页
export function getPmsBlackWordPage(params) {
  return request({
    url: '/transaction/oss/pmsBlackWord/wordPage',
    method: 'get',
    params
  })
}

// 加入黑、白名单，忽略
export function updateBlackType(data) {
  return request({
    url: '/transaction/oss/pmsBlackWord/updateBlackType',
    method: 'post',
    data
  })
}

// 新增黑白名单 敏感词
export function AddPmsBlackWordAddWord(data) {
  return request({
    url: '/transaction/oss/pmsBlackWord/addWord',
    method: 'post',
    data
  })
}

// 更新敏感词状态
export function updateWordStatus(data) {
  return request({
    url: '/transaction/oss/pmsBlackWord/updateWordStatus',
    method: 'post',
    data,
    params: {
      hideErrorMsg: true
    }
  })
}

// 二手车私信词库黑名单_私信记录_分页
export function pmsBlackWordPmsPage(params) {
  return request({
    url: '/transaction/oss/pmsBlackWord/wordPmsPage',
    method: 'get',
    params
  })
}

// 二手车贩黑名单_分页
export function getPmsBlackWordUserPage(params) {
  return request({
    url: '/transaction/oss/pmsBlackWord/userPage',
    method: 'get',
    params
  })
}

// 更新二手车贩黑名单状态
export function updateBlackUserStatus(data) {
  return request({
    url: '/transaction/oss/pmsBlackWord/updateBlackUserStatus',
    method: 'post',
    data
  })
}

// 添加黑名单用户
export function addBlackUse(data) {
  return request({
    url: '/transaction/oss/pmsBlackWord/addBlackUse',
    method: 'post',
    data
  })
}
// 二手车限流
export function riskManagerOpenFlow(data) {
  return request({
    url: '/uic/oss/risk/manager/open/flow',
    method: 'post',
    data
  })
}