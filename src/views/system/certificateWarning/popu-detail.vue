<template>
  <el-dialog title="证书详情" v-model="visible" width="30%">
    <el-form label-width="120px">
      <el-form-item label="证书类型">
        <el-select v-model="form.businessType" placeholder="请选择">
          <el-option
            v-for="item in certificateBusinessEnum"
            :key="item.id"
            :label="item.businessTypeName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="form.type" placeholder="请选择">
          <el-option
            v-for="(value, index) in CertificateEnum"
            :key="index"
            :label="value"
            :value="index"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="TypeEnum.isSSL" label="域名">
        <el-input v-model="form.domainName"></el-input>
      </el-form-item>
      <el-form-item v-if="TypeEnum.isSSL" label="部署厂商">
        <el-checkbox-group v-model="form.publishFactoryList">
          <el-checkbox
            style="display: block"
            v-for="item in companyList"
            :key="item.id"
            :label="item.id"
          >
            {{ item.factoryName }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="提醒时间">
        <el-date-picker
          v-model="form.reminderTime"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="选择日期时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="TypeEnum.isAndroid">
        <androidCertificate
          :version-num="form.version"
          @updateVersion="updateVersion"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="commitInfo">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref } from 'vue'
import { CertificateEnum } from '@/utils/enum'
import androidCertificate from '../androidCertificate/index.vue'
import {
  postCertificateAdd,
  postCertificateupdate,
  getCertificateFactory,
  getCertificateBusinessTypeList
} from '@/api/system'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
const visible = ref(false)
const itemId = ref(false)
const emits = defineEmits(['success'])

const certificateBusinessEnum = ref([])

const form = ref({
  businessType: '',
  type: '',
  domainName: '',
  publishFactoryList: [],
  reminderTime: '',
  version: ''
})

let originFactoryList = []

const TypeEnum = computed(() => {
  return {
    isSSL: form.value.type == 1,
    isAndroid: form.value.type == 2,
    isIos: form.value.type == 3 || form.value.type == 4 || form.value.type == 5
  }
})

const companyList = ref([])

const updateVersion = (version) => {
  form.value.version = version
  if (itemId.value) {
    form.value.reminderTime = dayjs().add(1, 'year').format('YYYY-MM-DD')
  }
}

const open = (item) => {
  init()
  getCertificateBusinessType()
  getCompanyList()
  visible.value = true
  if (item) {
    form.value.businessType = item.businessType
    form.value.type = item.type.toString()
    form.value.domainName = item.domainName
    form.value.reminderTime = dayjs(item.reminderTime).format('YYYY-MM-DD')
    if (item.publishFactoryIds) {
      form.value.publishFactoryList =
        item.publishFactoryIds.split(',')?.map((id) => Number(id)) || []
    }

    originFactoryList = JSON.parse(
      JSON.stringify(form.value.publishFactoryList)
    )
    form.value.version = item.version.toString()
    itemId.value = item.id
  } else {
    itemId.value = ''
  }
}

const init = () => {
  form.value = {
    businessType: '',
    type: '',
    domainName: '',
    publishFactoryList: [],
    reminderTime: '',
    version: ''
  }
  itemId.value = ''
  originFactoryList = []
}

const close = () => {
  visible.value = false
}

const getCompanyList = () => {
  getCertificateFactory().then((res) => {
    if (res.data.code == 0) {
      companyList.value = res.data.data || []
    }
  })
}

// 获取业务类型
const getCertificateBusinessType = () => {
  getCertificateBusinessTypeList().then((res) => {
    if (res.data.code == 0) {
      const data = res.data.data || []
      certificateBusinessEnum.value = data || {}
    }
  })
}

const commitInfo = () => {
  if (!form.value.businessType) {
    return ElMessage.error('请选择业务')
  }
  if (!form.value.type) {
    return ElMessage.error('请选择类型')
  }

  if (
    TypeEnum.value.isSSL &&
    itemId.value &&
    originFactoryList.some((id) => !form.value.publishFactoryList?.includes(id))
  ) {
    ElMessage.error('请至少勾选原部署厂商')
    return
  }

  if (!form.value.reminderTime) {
    return ElMessage.error('请选择提醒时间')
  }

  const url = itemId.value ? postCertificateupdate : postCertificateAdd
  const params = {
    ...form.value,
    publishFactoryIds: form.value.publishFactoryList.join(',') || '',
    id: itemId.value ? itemId.value : ''
  }
  delete params.publishFactoryList
  url(params).then((res) => {
    if (res.data.code == 0) {
      ElMessage.success(res.data.msg)
      emits('success')
      close()
    }
  })
}

defineExpose({
  open
})
</script>
<style lang="scss" scoped></style>
