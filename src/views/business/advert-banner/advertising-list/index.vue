/** * 广告列表 */
<template>
  <div class="advertising-list">
    <header style="margin-bottom: 10px">
      <el-form :model="ruleForm" :inline="true">
        <el-form-item label="广告ID">
          <el-input
            v-model="ruleForm.adUnitId"
            type="text"
            placeholder="请输入广告ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="一级页面">
          <el-select
            v-model="ruleForm.siteSetFirstId"
            @change="changeLevelPage(1)"
            clearable
          >
            <el-option
              v-for="(value, index) in siteSetFirsetList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="二级页面">
          <el-select
            v-model="ruleForm.siteSetSecondId"
            @change="changeLevelPage(2)"
            clearable
          >
            <el-option
              v-for="(value, index) in siteSetSecondList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="三级页面">
          <el-select v-model="ruleForm.siteSetThirdId" clearable>
            <el-option
              v-for="(value, index) in siteSetThirdList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告类型">
          <el-select v-model="ruleForm.adTypeId" clearable>
            <el-option
              v-for="(value, index) in typeList"
              :key="index"
              :label="value.name"
              :value="value.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属广告方ID">
          <el-input
            v-model="ruleForm.orgId"
            type="text"
            placeholder="请输入所属广告方ID"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="所属广告方">
          <el-input
            v-model="ruleForm.orgName"
            type="text"
            placeholder="请输入所属广告方"
            clearable
            style="width: 180x"
          />
        </el-form-item>
        <el-form-item label="所属广告方类型">
          <el-select v-model="ruleForm.orgType" clearable>
            <el-option
              v-for="(value, index) in advertisingType"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划ID">
          <el-input
            v-model="ruleForm.campaignId"
            type="text"
            placeholder="请输入计划ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="计划名称">
          <el-input
            v-model="ruleForm.campaignName"
            type="text"
            placeholder="请输入计划名称"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="广告名称">
          <el-input
            v-model="ruleForm.adName"
            type="text"
            placeholder="请输入广告名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="广告位置">
          <el-select v-model="ruleForm.position" clearable>
            <el-option
              v-for="(value, index) in advertisingPositionRange"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
          <!-- <el-input v-model="ruleForm.position" type="text" placeholder="请输入广告位置" clearable style="width: 200px" /> -->
        </el-form-item>
        <el-form-item label="广告刷数">
          <el-select v-model="ruleForm.refreshCount" clearable>
            <el-option
              v-for="(value, index) in advertisingCountRange"
              :key="index"
              :label="value"
              :value="value"
            />
          </el-select>
          <!-- <el-input v-model="ruleForm.refreshCount" type="text" placeholder="请输入广告刷数" clearable style="width: 200px" /> -->
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="ruleForm.statusType">
            <el-option
              v-for="(value, index) in statusTypeList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否有效">
          <el-select v-model="ruleForm.status">
            <el-option
              v-for="(value, index) in effective"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投放区域(省)">
          <el-input
            v-model="ruleForm.province"
            type="text"
            placeholder="投放区域(省)"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="投放区域(市)">
          <el-input
            v-model="ruleForm.city"
            type="text"
            placeholder="投放区域(市)"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="有效时间段" label-width="100px">
          <el-date-picker
            v-model="daterange"
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            :picker-options="pickerOptions"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="发布开始日期"
            end-placeholder="发布结束日期"
          />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="ruleForm.platform">
            <el-option
              v-for="(value, index) in platList"
              :key="index"
              :label="index"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" label-width="100px">
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) =>
                $dayjs(d, 'hh:mm:ss').toDate()
              )
            "
            v-model="createDaterange"
            :picker-options="pickerOptions"
            style="width: 400px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="发布开始日期"
            end-placeholder="发布结束日期"
          />
        </el-form-item>
        <el-form-item label="创建人">
          <el-autocomplete
            v-model="operatorName"
            clearable
            :fetch-suggestions="querySearchAsync"
            @select="updateRemindId"
            @clear="this.ruleForm.operatorId = ''"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" @click="resetData">重置</el-button>
          <el-button type="primary" @click="openEdit">创建广告</el-button>
          <!-- <aliUpload></aliUpload> -->
        </el-form-item>
      </el-form>
    </header>
    <div style="min-height: 80vh">
      <el-table
        ref="inquiryRecordList"
        :data="inquiryRecordList"
        class="inquiryRecordList"
        highlight-current-row
        row-key="inquiryRecordList"
        max-height="650"
        @row-dblclick="openEdit"
        @sort-change="sortSearchList"
      >
        <el-table-column prop="adUnitId" label="广告ID" align="center" fixed />
        <el-table-column
          prop="siteSetFirst"
          label="一级页面"
          align="center"
          fixed
        />
        <el-table-column
          prop="siteSetSecond"
          label="二级页面"
          align="center"
          fixed
        />
        <el-table-column
          prop="siteSetThird"
          label="三级页面"
          align="center"
          fixed
        />
        <el-table-column
          prop="adTypeName"
          label="广告类型"
          align="center"
          fixed
        />
        <el-table-column
          prop="orgId"
          label="所属广告方ID"
          align="center"
          width="120px"
        />
        <el-table-column
          prop="orgName"
          label="所属广告方"
          align="center"
          width="100px"
        />
        <el-table-column
          prop="advertiserTypeName"
          label="所属广告方类型"
          align="center"
          width="150px"
        />
        <el-table-column prop="campaignId" label="计划ID" align="center" />
        <el-table-column prop="campaignName" label="计划名称" align="center">
          <template v-slot="scope">
            <el-popover placement="bottom" :width="400" trigger="click">
              <template #reference>
                <span @click="getCampaignData(scope.row)">{{
                  scope.row.campaignName
                }}</span>
              </template>
              <div>
                <div>
                  总量：{{ campaignData.controlNumberPredict || '不限' }}
                </div>
                <div>已投放量：{{ campaignData.servingNum }}</div>
                <div>
                  可用余量：{{
                    !campaignData.controlNumberPredict
                      ? '不限'
                      : campaignData.servingLeftNum
                  }}
                </div>
                <div>实际曝光：{{ campaignData.exposureNum }}</div>
                <div>
                  实际余量：{{
                    !campaignData.controlNumberPredict
                      ? '不限'
                      : campaignData.exposureLeftNum
                  }}
                </div>
                <div>备注：{{ campaignData.remark }}</div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="adName" label="广告名称" align="center" />
        <el-table-column
          prop="position"
          label="广告位置"
          align="center"
          width="110px"
          sortable="custom"
        />
        <el-table-column prop="refreshCount" label="广告刷数" align="center" />
        <el-table-column prop="linkUrl" label=" 配置ID" align="center">
          <template v-slot="scope">
            <span>{{
              $match.checkType(scope.row.linkUrl, 'url')
                ? ''
                : scope.row.linkUrl
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="statusType" label="状态" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.statusDesc }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="150px">
          <template v-slot:header>
            <span>操作</span>
            <el-tooltip placement="bottom">
              <template #content>
                <div>
                  说明：<br />
                  复制：支持复制广告；<br />
                  暂停：暂停广告后，剩余投放量仍保留；<br />
                  恢复：恢复广告后，继续投放广告；
                </div>
              </template>
              <el-button style="margin-top: -3px" type="primary" link
                >?</el-button
              >
            </el-tooltip>
          </template>
          <template v-slot="scope">
            <el-button size="small" type="primary" @click="copyRow(scope.row)"
              >复制</el-button
            >
            <el-button
              v-if="['已暂停'].includes(scope.row.statusDesc)"
              size="small"
              type="primary"
              @click="moreIOperation(scope.row, 1)"
              >恢复</el-button
            >
            <el-button
              v-if="['未开始', '进行中'].includes(scope.row.statusDesc)"
              size="small"
              type="primary"
              @click="moreIOperation(scope.row, 0)"
              >暂停</el-button
            >
            <el-button
              v-if="['未启动'].includes(scope.row.statusDesc)"
              size="small"
              type="primary"
              @click="moreIOperation(scope.row, 1)"
              >启动</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="controlNumber" label="投放总量" align="center" />
        <el-table-column label="剩余曝光" align="center">
          <template v-slot="scope">
            <span>{{
              scope.row.controlNumber !== '-' && scope.row.exposurePv
                ? scope.row.controlNumber - scope.row.exposurePv
                : '-'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="exposurePv" label="曝光PV" align="center" />
        <el-table-column prop="clickPv" label="点击PV" align="center" />
        <el-table-column prop="controlNumber" label="CTR" align="center">
          <template v-slot="scope">
            <span>{{
              scope.row.clickPv && scope.row.exposurePv
                ? ((scope.row.clickPv / scope.row.exposurePv) * 100).toFixed(
                    2
                  ) + '%'
                : 0
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="region" label="投放区域" align="center" />
        <el-table-column prop="platform" label="投放平台" align="center">
          <template v-slot="scope">
            <div>{{ scope.row.platform }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="isRepeat" label="每页展示" align="center" /> -->
        <el-table-column prop="operatorName" label="创建人" align="center" />
        <el-table-column
          align="center"
          label="广告有效时间"
          width="200px"
          prop="beginTime"
          sortable="custom"
        >
          <template v-slot="scope">
            <span>{{ scope.row.beginTime }}<br />{{ scope.row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          align="center"
          width="200px"
        >
          <template v-slot="scope">{{ scope.row.createTime }}</template>
        </el-table-column>
        <el-table-column prop="status" label="是否有效" align="center">
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.showStatus"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="changeStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作日志" align="center">
          <template v-slot="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="seeLog(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        :page-size="20"
        :page-sizes="[10, 20, 40, 60]"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        style="text-align: center; margin-top: 10px"
        @size-change="currentChange"
        @current-change="currentChange"
      />
      <el-dialog v-model="dialogFormVisible" title="提示" width="30%">
        <div>
          {{ `本次广告与${repeatData.repeatId}(广告ID)重复，是否继续配置` }}
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="goToCheck">去查看</el-button>
            <el-button
              v-if="!repeatData.shopFlag"
              type="primary"
              @click="continueSet"
              >继续配置</el-button
            >
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="dialogFormVisible1" title="提示" width="30%">
        <div>
          {{ repeatData.msg }}
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="goSyncData(false)">不同步</el-button>
            <el-button type="primary" @click="goSyncData(true)"
              >同步并启动</el-button
            >
          </div>
        </template>
      </el-dialog>
    </div>
    <seeLog ref="seeLog" />
    <!-- <SeeLog2 ref="SeeLog2" /> -->
  </div>
</template>

<script>
import { advertisingType, advertisingRange } from '@/utils/enum/adConfigEnum.js'
import { pickerOptions } from '@/utils/configData'
import {
  getAdvertisingList,
  postAdvertisingStateUpdate,
  getAdvertiserTypeAll,
  getAdvertisingNextLevelPage,
  getAdvertisingPlanList
} from '@/api/advertModuleNew'
import { platformList } from '@/utils/enum'
import { resetData } from '@/utils'
import { getOssAuditName } from '@/api/advertModule'
import seeLog from '@/components/seeLog/SeeLog.vue'
// import SeeLog2 from '../components/see-log.vue'

export default {
  name: 'AdvertisingList',
  components: {
    // aliUpload
    seeLog
    // SeeLog2
  },
  data() {
    return {
      pickerOptions,
      effective: {
        全部: '',
        有效: '1',
        无效: '-1'
      }, // 是否有效
      statusTypeList: {
        全部: '',
        未启动: 1,
        未开始: 2,
        进行中: 3,
        已暂停: 4,
        到期结束: 5,
        到量结束: 6,
        已无效: 7
      },
      dialogFormVisible: false, //创建广告dialog
      dialogFormVisible1: false, //创建广告dialog
      siteSetFirsetList: [], //一级页面list
      siteSetSecondList: [], //二级页面list
      siteSetThirdList: [], //
      existAD: '', //已存在广告
      advertisingPositionRange: advertisingRange, //广告位置位置
      advertisingCountRange: advertisingRange, //广告刷数
      otherForm: {
        type: '',
        thirdLinkType: '',
        typeName: '',
        advertiserName: ''
      },
      ruleForm: {
        status: '', // 是否有效1有效0无效
        adUnitId: '', // 广告id
        siteSetFirstId: '', //一级页面id
        siteSetSecondId: '', //二级页面id
        siteSetThirdId: '', //三级页面id
        adTypeId: '', //广告类型id
        orgId: '', //所属广告方id
        orgName: '', //所属广告方名称
        orgType: '', //所属广告方类型
        campaignId: '', //计划id
        campaignName: '', //计划名称
        adName: '', //广告名称
        refreshCount: '', //第几刷
        position: '', //广告位置
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        province: '', //省
        city: '', //城市
        platform: '', // 平台
        statusType: '', // 状态
        createBeginTime: '', // 创建时间（开始）
        createEndTime: '', // 创建时间（结束）
        operatorId: '', // 用户id
        orderBy: '' // 指按照哪个字段排序
      },
      advertisingType: { 全部: '', ...advertisingType }, //广告方类型
      advertiserType: [], //广告类型
      repeatData: { repeatId: '' }, //重复广告数据
      changeRowData: {}, //所改变一行的数据
      platList: {
        全部: '',
        Android: '1',
        iOS: '2',
        鸿蒙: '31'
      },
      page: 1, // 页码
      total: 0, // 总数
      inquiryRecordList: [], // 配置内容数据
      platformList: {
        ...platformList,
        31: '鸿蒙'
      },
      campaignData: {}, // 投放计划数据
      operatorName: '' // 提醒人名称
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    },
    // 广告类型列表
    typeList() {
      let typeList = [...this.advertiserType]
      if (this.ruleForm.siteSetFirstId) {
        this.siteSetFirsetList.map((_) => {
          if (_.id === this.ruleForm.siteSetFirstId) {
            typeList = _.typeList || []
          }
        })
        if (this.ruleForm.siteSetSecondId) {
          this.siteSetSecondList.map((_) => {
            if (_.id === this.ruleForm.siteSetSecondId) {
              typeList = _.typeList || []
            }
          })
          if (this.ruleForm.siteSetThirdId) {
            this.siteSetThirdList.map((_) => {
              if (_.id === this.ruleForm.siteSetThirdId) {
                typeList = _.typeList || []
              }
            })
          }
        }
      }
      return typeList
    },
    createDaterange: {
      get() {
        if (this.ruleForm.createBeginTime && this.ruleForm.createEndTime) {
          return [this.ruleForm.createBeginTime, this.ruleForm.createEndTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.createBeginTime = value[0]
          this.ruleForm.createEndTime = value[1]
        } else {
          this.ruleForm.createBeginTime = ''
          this.ruleForm.createEndTime = ''
        }
      }
    }
  },
  async activated() {
    const oldQuery = sessionStorage.getItem('AdvertisingList') || '{}'
    const getStatus = sessionStorage.getItem('AdvertisingListNew') || false
    if (getStatus) {
      sessionStorage.removeItem('AdvertisingListNew')
      this.resetDataOnly()
      await this.setParams()
      const me = this
      setTimeout(() => {
        me.search()
      }, 100)
      return
    }
    this.ruleForm = {
      ...this.ruleForm,
      ...JSON.parse(oldQuery)
    }
    const addId = sessionStorage.getItem('adIdOld')
    if (addId === '') {
      sessionStorage.removeItem('adIdOld')
    }
    this.getList({
      limit: this.ruleForm.limit || 20,
      page: this.ruleForm.page || 1
    })
  },
  mounted() {
    this.getAdvertisingLevelPage(0)
    this.getAdvertiserTypeAll() //获取全部广告类型列表
    // await this.setParams()
    // const me = this
    // setTimeout(() => {
    //   me.search()
    // }, 400)
  },
  methods: {
    setParams() {
      if (this.$route.query.repeatData) {
        this.repeatData = JSON.parse(
          decodeURIComponent(this.$route.query.repeatData)
        )
        return this.setSearchData()
      }
      const query = this.$route.query
      this.ruleForm.orgId = query.orgId
      this.ruleForm.orgName = query.orgName
      this.ruleForm.orgType = query.orgType ? Number(query.orgType) : ''
      this.ruleForm.campaignId = query.campaignId
      this.ruleForm.campaignName = query.campaignName
      this.ruleForm.orgRelationId = query.orgRelationId
      this.ruleForm.adTypeId = query.adTypeId ? Number(query.adTypeId) : ''
    },

    //获取全部广告类型
    getAdvertiserTypeAll() {
      getAdvertiserTypeAll()
        .then((response) => {
          this.advertiserType = response.data.data || []
        })
        .catch((_) => {
          console.log(_)
        })
    },
    // 改变是否有效
    changeStatus(item) {
      const me = this
      if (item.showStatus) {
        item.showStatus = !item.showStatus
        return me.$message.error('广告无效后不可再开启，请重新创建')
      }
      me.$confirm(
        '无效后，广告不可再开启，且剩余投放量将释放，是否确认无效？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          me.changeRowData = item
          // console.log(item)
          // return
          me.checkStateUpdate(item.showStatus, false, -1)
        })
        .catch(() => {
          item.showStatus = !item.showStatus
        })
    },
    // 恢复/暂停，启动 操作
    moreIOperation(item, status) {
      this.changeRowData = item
      this.checkStateUpdate(status ? true : false, false, status)
    },
    //改变状态
    checkStateUpdate(isCheck, isContinue, status) {
      const item = this.changeRowData
      const me = this
      // console.log({
      postAdvertisingStateUpdate({
        adUnitId: item.adUnitId,
        status:
          status !== null ? status : item.showStatus || isContinue ? 1 : -1,
        checkEssayConflict: 1, // 内容重复
        checkConflict: isCheck ? 1 : 0 //校验是否有重复
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            if (data && data.code === 91) {
              // 内容重复
              me.repeatData = {
                ...data,
                msg: response.data.msg || ''
              }
              me.dialogFormVisible1 = true
            } else if (data && data.repeatId) {
              // item.showStatus = !item.showStatus
              this.repeatData = data
              this.dialogFormVisible = true
            } else {
              if (isContinue) {
                item.showStatus = !item.showStatus
              }
              me.$message.success('修改成功')
              me.getList(
                {
                  limit: 20,
                  page: me.page
                },
                me.ruleForm.statusType ? {} : item
              )
            }
          } else {
            item.showStatus = false
          }
        })
        .catch((_) => {
          const err = _.response.data.code || ''
          if (err === 74) return
          item.showStatus = !item.showStatus
          item.statusOld = !item.statusOld
          console.log(_)
        })
    },
    // 广告层级获取
    getAdvertisingLevelPage(level) {
      let list = ''
      let parentId = ''
      switch (level) {
        case 0:
          list = 'siteSetFirsetList'
          parentId = 0
          break
        case 1:
          list = 'siteSetSecondList'
          parentId = this.ruleForm.siteSetFirstId
          this.ruleForm.siteSetSecondId = ''
          this.ruleForm.siteSetThirdId = ''
          this.siteSetSecondList = []
          this.siteSetThirdList = []
          break
        case 2:
          list = 'siteSetThirdList'
          parentId = this.ruleForm.siteSetSecondId
          this.ruleForm.siteSetThirdId = ''
          this.siteSetThirdList = []
          break
      }
      // this.typeList = []
      this.ruleForm.adTypeId = ''
      if (!parentId && level) return
      this.getAdvertisingNextLevelPage(list, parentId)
    },
    getAdvertisingNextLevelPage(list, parentId) {
      getAdvertisingNextLevelPage({ parentId: parentId })
        .then((response) => {
          this[list] = response.data.data
        })
        .catch((_) => {
          console.log(_)
        })
    },
    changeLevelPage(level) {
      this.getAdvertisingLevelPage(level)
    },
    // 更新页码
    currentChange(page) {
      const me = this
      me.page = page
      me.inquiryRecordList = []
      me.getList({
        limit: 20,
        page: page
      })
    },
    // 查询
    search() {
      const me = this
      me.page = 1
      me.getList({
        limit: 20,
        page: 1
      })
    },
    // 获取列表数据
    getList(paramsObj, itemObj) {
      const me = this
      const requestParams = {
        ...me.ruleForm,
        ...paramsObj,
        advertPlatform: 1
      }
      sessionStorage.setItem('AdvertisingList', JSON.stringify(requestParams))
      me.inquiryRecordList =
        itemObj && itemObj.adUnitId ? me.inquiryRecordList || [] : []
      getAdvertisingList(requestParams)
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            data.listData = data.listData || []
            data.listData.map((v) => {
              v.showStatus = v.status !== -1
              v.isRepeat = v.isRepeat == 1 ? '是' : '否'
              v.region = v.region ? v.region : '全国'
              v.region = me.splitBackData(v.region, 3, '个区域')
              if (v.platform) {
                v.platform = v.platform
                  .split(',')
                  .map((_) => (_ = me.platformList[parseInt(_)]))
                  .join(' ')
              }
              return v
            })
            me.page = requestParams.page
            me.total = data.total
            if (itemObj && itemObj.adUnitId) {
              // 仅刷新单条逻辑
              const findIndex = data.listData.findIndex((item) => {
                return item.adUnitId === itemObj.adUnitId
              })
              if (findIndex >= 0) {
                me.inquiryRecordList.splice(
                  findIndex,
                  1,
                  data.listData[findIndex]
                )
              }
              return
            }
            me.inquiryRecordList = data.listData || []
          }
        })
        .catch((e) => {
          console.log(e)
        })
    },
    //去查看
    async goToCheck() {
      // this.resetDataOnly()
      // await this.setSearchData()
      // this.search()
      sessionStorage.setItem('AdvertisingListNew', true)
      const toTaskLog = this.$router.resolve({
        name: 'AdvertisingList',
        query: {
          repeatData: encodeURIComponent(JSON.stringify(this.repeatData))
        }
      })
      window.open(toTaskLog.href, '_blank')
      this.dialogFormVisible = false
    },
    //继续配置
    continueSet() {
      this.dialogFormVisible = false
      this.checkStateUpdate(false, true, 1)
    },
    async setSearchData() {
      // this.ruleForm = { ...this.ruleForm, ...this.repeatData }
      // delete this.ruleForm.repeatId
      // delete this.ruleForm.shopFlag
      await this.getAdvertisingNextLevelPage(
        'siteSetSecondList',
        this.repeatData.siteSetFirstId
      )
      await this.getAdvertisingNextLevelPage(
        'siteSetThirdList',
        this.repeatData.siteSetSecondId
      )
      this.ruleForm.adTypeId = this.repeatData.adTypeId
      this.ruleForm.refreshCount = this.repeatData.refreshCount
      this.ruleForm.position = this.repeatData.position
      this.ruleForm.beginTime = this.repeatData.beginTime
      this.ruleForm.endTime = this.repeatData.endTime
      this.ruleForm.province = this.repeatData.province
      this.ruleForm.city = this.repeatData.city
      this.ruleForm.siteSetFirstId = this.repeatData.siteSetFirstId
      this.ruleForm.siteSetSecondId = this.repeatData.siteSetSecondId
      this.ruleForm.siteSetThirdId = this.repeatData.siteSetThirdId
    },
    //初始化
    resetDataOnly() {
      this.ruleForm = {
        status: '', // 是否有效1有效0无效
        adUnitId: '', // 广告id
        siteSetFirstId: '', //一级页面id
        siteSetSecondId: '', //二级页面id
        siteSetThirdId: '', //三级页面id
        adTypeId: '', //广告类型id
        orgId: '', //对象id
        orgName: '', //对象名称
        orgType: '', //对象类型
        campaignId: '', //计划id
        campaignName: '', //计划名称
        adName: '', //广告名称
        refreshCount: '', //第几刷
        position: '', //广告位置
        beginTime: '', // 开始时间
        endTime: '', // 结束时间
        province: '', //省
        city: '', //城市
        platform: '', // 平台
        statusType: '', // 状态
        createBeginTime: '', // 创建时间（开始）
        createEndTime: '', // 创建时间（结束）
        operatorId: '', // 用户id
        orderBy: '' // 有效时间排序
      }
      this.operatorName = ''
    },
    //重置
    resetData() {
      this.$refs.inquiryRecordList.clearSort()
      this.resetDataOnly()
      this.search()
    },
    // 查看配置详情
    goToDetail(row, copy) {
      const me = this
      // console.log(row)
      const params = {}
      if (row.adUnitId) {
        params.id = row.adUnitId
      }
      if (copy) {
        params.copy = copy
      }
      const isOrgIdStatus =
        me.ruleForm.orgId && me.inquiryRecordList && me.inquiryRecordList.length
      if (isOrgIdStatus && !copy && !row.adUnitId) {
        // 有广告id且有列表数据，取列表第一条带入详情
        const firstList = me.inquiryRecordList[0]
        params.orgId = firstList.orgId
        params.orgType = firstList.advertiserType
        params.campaignId = firstList.campaignId || ''
      }
      me.$router.push({
        name: 'AdDetailsConfig',
        query: params
      })
    },

    copyRow(row) {
      this.goToDetail(row, true)
    },
    //进入广告配置
    openEdit(row) {
      this.goToDetail(row)
    },
    // 清除所有数据
    clearAllData() {
      resetData(this)
    },
    // 查看日志
    seeLog(data) {
      this.$refs.seeLog.init('48', data.adUnitId)
    },
    querySearchAsync(queryString, cb) {
      if (!queryString) return cb([])
      getOssAuditName({
        nameOrCode: queryString
      })
        .then((res) => {
          if (res.data.code === 0) {
            const listData = res.data.data || []
            const cbData = listData.map((item) => {
              return {
                ...item,
                value: item.userName
              }
            })
            return cb(cbData)
          } else {
            this.$message.error(res.data.msg)
          }
        })
        .catch((e) => {
          this.$message.error(e.message)
        })
    },
    // 更新提醒人id
    updateRemindId(val) {
      this.ruleForm.operatorId = val.userId || ''
    },
    // 仅展示部分数据
    splitBackData(data, postion, endTip) {
      let newData = data ? data.replace(/,/g, '、') : ''
      const showGoodsNamesLength = (newData && newData.split('、')) || 0
      if (showGoodsNamesLength.length > postion) {
        const showGoodsNamesList = newData.split('、')
        newData =
          newData.slice(0, newData.indexOf(showGoodsNamesList[postion]) - 1) +
          '等' +
          showGoodsNamesList.length +
          endTip
      }
      return newData
    },
    // 获取数据
    getCampaignData(data) {
      const me = this
      getAdvertisingPlanList({
        campaignId: data.campaignId,
        page: 1,
        limit: 20
      })
        .then((response) => {
          if (response.data.code === 0) {
            const data = response.data.data
            const dataList = data.list || []
            me.campaignData = dataList[0] || {}
          }
        })
        .catch(() => {})
    },
    // 筛选
    sortSearchList(item) {
      console.log(item)
      if (item.prop === 'position') {
        this.ruleForm.sortType = item.order === 'ascending' ? '1' : '2'
        this.ruleForm.orderBy = 'position'
      } else if (item.prop === 'beginTime') {
        this.ruleForm.sortType = item.order === 'ascending' ? '1' : '0'
        this.ruleForm.orderBy = 'beginTime'
      } else {
        this.ruleForm.orderBy = ''
        this.ruleForm.sortType = ''
      }
      this.getList({
        page: this.page
      })
    },
    // 同步数据
    goSyncData(status) {
      const me = this
      if (!status) {
        me.dialogFormVisible1 = false
        return me.$message.warning('启动失败')
      }
      me.dialogFormVisible1 = false
      postAdvertisingStateUpdate({
        adUnitId: me.changeRowData.adUnitId,
        status: 1,
        checkEssayConflict: 3, //
        position: me.repeatData.position,
        refreshCount: me.repeatData.refreshCount
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('修改成功')
            me.getList({
              limit: 20,
              page: me.page
            })
          }
        })
        .catch((_) => {
          console.log(_)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.advertising-list {
  padding: 10px 20px;

  .inquiryRecordList {
    width: 100%;
    height: 75vh;
    overflow-y: auto;

    .provinceAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .cityAddress {
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
