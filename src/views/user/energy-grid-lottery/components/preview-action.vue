<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    append-to-body
    title="预览活动"
    width="30%"
  >
    <div class="flex-center">
      <div class="box">
        <div class="box-box">
          <div class="energy-icon"></div>
          <div class="energy-box"></div>
          <div class="box-item-box">
            <div
              class="box-item"
              v-for="(item, index) in list"
              :key="index"
              :class="[
                { 'border-box': 0 === index },
                { 'start-bg': index === 4 }
              ]"
            >
              <div class="item-center" :class="{ start: index === 4 }">
                <img
                  :src="`/static/img/${
                    item.img || 'detail/<EMAIL>'
                  }`"
                  alt=""
                />
                <div>{{ item.awardName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleClose">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, defineExpose } from 'vue'
import { getTurntableDetail } from '@/api/user'

const dialogVisible = ref(false)
const form = ref({})
const activityAwardList = ref([])
const id = ref('')

const open = (row) => {
  dialogVisible.value = true
  id.value = row.id || ''
  if (id.value) {
    getData()
  }
}

const getData = async () => {
  const { data } = await getTurntableDetail({
    id: id.value
  })
  const record = data.data || {}
  activityAwardList.value = record.activityAwardList
  form.value = record || {}
}
const awardTypeList = {
  2: 'energy-prize.png',
  3: 'jdcard-prize.png',
  1: 'fail-prize.png'
}
const list = computed(() => {
  const award = activityAwardList.value?.map((item) => {
    return {
      ...item,
      img: awardTypeList[item.awardType]
    }
  })
  award?.splice(4, 0, {
    awardAmount: 0,
    awardName: `${form.value.useEnergyCnt}能量/次`,
    awardType: 0,
    awardId: 4,
    awardRatio: '',
    img: 'start-prize.png'
  })
  return award
})

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.box {
  // position: absolute;
  width: 50%;
  // top: 50%;
  // left: 50%;
  // transform: translate(-50%, -50%);
  // background-image: url(/static/img/assets/energy/energy-background-box.png);
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
  border: 1px solid #ff9250;
  border-radius: 10px;
  padding: 0.1rem;

  .box-box {
    position: relative;
    // background-image: url(/static/img/assets/energy/box-background.png);
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
  }

  .box-item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 5px;
  }

  .energy-num {
    color: #ff3e3a;
    font-size: 0.16rem;
  }

  .box-item {
    width: 30%;
  }

  .start-bg {
    background: linear-gradient(180deg, #ff9250 0%, #ff4000 100%);
  }

  .box-item {
    width: calc(calc(100% - 10px) / 3);
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    // border: 0.04rem solid transparent;
    background-color: white;

    .item-center {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      font-size: 13px;
      line-height: 20px;
    }

    img {
      width: 100%;
      height: 100%;
      margin-bottom: 0.05rem;
    }
  }

  .start {
    color: #ffeba7;

    img {
      width: 45px;
      height: 45px;
      margin-bottom: 10px;
    }
  }

  .border-box {
    background: url(/static/img/assets/energy/box-item.png);
    background-position: 100% 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: white;
  }

  .box-item::before {
    content: '';
    display: block;
    padding-top: 100%;
  }

  .btn-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0.2rem;
    color: white;
    font-size: 0.2rem;
    font-weight: bold;
    margin-bottom: 0.1rem;
    position: relative;
  }

  .btn1 {
    flex: 1;
    background: url(/static/img/assets/energy/btn1.png) no-repeat;
    // height: 0.8rem;
    padding: 0.2rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: 100% 100%;
  }

  .btn2 {
    flex: 2;
    background: url(/static/img/assets/energy/btn2.png) no-repeat;
    // height: 0.8rem;
    padding: 0.2rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: 100% 100%;
    margin-left: 0.05rem;
  }

  .btn2-tip {
    position: absolute;
    top: -0.1rem;
    right: 0.1rem;
    background-color: white;
    font-size: 0.12rem;
    color: #333333;
    padding: 0.03rem 0.08rem;
  }

  .tip {
    color: #ff703f;
    font-size: 0.12rem;
  }
}
</style>
