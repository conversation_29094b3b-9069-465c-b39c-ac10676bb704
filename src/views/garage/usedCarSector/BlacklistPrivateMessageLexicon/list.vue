<template>
  <div>
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="敏感词">
        <el-input
          v-model="ruleForm.words"
          type="text"
          placeholder="请输入敏感词"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="ruleForm.wordType">
          <el-option
            v-for="(value, index) in typeList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="审核状态" v-if="isBlack">
        <el-select v-model="ruleForm.auditStatus">
          <el-option
            v-for="(value, index) in auditStatusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="关联用户数">
        <el-select v-model="ruleForm.userNumbers">
          <el-option
            v-for="(value, index) in userNumList"
            :key="index"
            :label="value"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="触发次数">
        <el-select v-model="ruleForm.plidNumbers">
          <el-option
            v-for="(value, index) in pmsNumList"
            :key="index"
            :label="value"
            :value="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="有效状态" v-if="isBlack">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" v-if="!isBlack">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          v-model="createDaterange"
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          clearable
          range-separator="—"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          align="center"
        />
      </el-form-item>
      <el-form-item>
        <div class="ml20">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button
            v-if="isBlack"
            class="fl-right"
            type="primary"
            @click="openAddDialog"
            >新增</el-button
          >
          <el-button
            v-if="isBlack"
            class="fl-right"
            type="primary"
            @click="goSeeLog"
            >操作日志</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <el-table :data="listData" border height="70vh">
      <el-table-column prop="id" align="center" label="ID" />
      <el-table-column prop="word" align="center" label="敏感词" />
      <el-table-column prop="type" align="center" label="类型">
        <template v-slot="{ row }">
          <span>{{ typeEnum[row.wordType] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="relatedUserNum"
        align="center"
        label="关联用户数"
      />
      <el-table-column prop="pmsNum" align="center" label="触发次数" />
      <el-table-column type="name" align="center" label="私信对话查询">
        <template v-slot="{ row }">
          <el-button type="primary" link @click="goPrivacy(row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="有效状态" align="center" v-if="isBlack">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.status"
            @change="(state) => changeState(scope.row, state)"
            :active-value="1"
            :inactive-value="0"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        align="center"
        label="备注"
        v-if="isBlack"
      />
      <el-table-column align="center" label="操作人" v-if="isBlack">
        <template v-slot="{ row }">
          <div>
            {{ row.operatorName || '系统' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="joinTime"
        label="入场时间"
        align="center"
        v-if="isBlack"
      />
      <el-table-column
        label="操作"
        align="center"
        :width="!isBlack ? 300 : 105"
      >
        <template v-slot="{ row }">
          <template v-if="!isBlack">
            <el-button type="primary" link @click="operation(row, 1)"
              >加入黑名单</el-button
            >
            <el-button type="primary" link @click="operation(row, 2)"
              >移动至白名单</el-button
            >
            <el-button type="primary" link @click="operation(row, 3)"
              >忽略</el-button
            >
          </template>
          <el-button type="primary" v-if="isBlack" link @click="openLog(row)"
            >日志</el-button
          >
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        v-if="!isBlack"
      />
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @current-change="currentChange"
    />
    <el-dialog v-model="dialogVisible" title="新增" width="600">
      <el-form :model="dialogRuleForm" label-width="70px">
        <el-form-item label="词库" required>
          <el-input
            v-model="dialogRuleForm.words"
            type="textarea"
            :rows="3"
            :autosize="{ minRows: 3 }"
            resize="none"
            placeholder="请输入敏感词，按,分开"
          />
        </el-form-item>
        <el-form-item label="类型" required>
          <el-radio-group v-model="dialogRuleForm.wordType">
            <el-radio
              v-for="(item, key) in resultTypeEnum"
              :key="key"
              :label="Number(key)"
              >{{ item }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="dialogRuleForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer text-center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmation"> 确认 </el-button>
      </div>
    </el-dialog>
    <seeLog ref="seeLogRef" />
    <chat ref="chatRef" />
  </div>
</template>

<script>
import { usedCarspickerOptions } from '@/utils/configData'
import { changeDate, format } from '@/utils/configData/format'
import {
  getPmsBlackWordPage,
  updateBlackType,
  AddPmsBlackWordAddWord,
  updateWordStatus
} from '@/api/usedCarPublish'
import { postLog } from '@/components/seeLog/SaveLog.js'
import seeLog from '@/components/seeLog/SeeLog.vue'
import chat from './chat.vue'

export default {
  name: 'BlacklistPrivateMessageLexiconList',
  props: {
    isBlack: {
      type: Boolean,
      default: false
    }
  },
  components: {
    seeLog,
    chat
  },
  data() {
    return {
      pickerOptions: usedCarspickerOptions,
      logDialogVisible: false,
      ruleForm: {
        words: '',
        wordType: '',
        auditStatus: '',
        status: '',
        userNumbers: 0,
        plidNumbers: 0,
        beginTime: '',
        endTime: ''
      },
      page: 1,
      total: 0,
      limit: 20,
      typeList: {
        全部: '',
        微信: '2',
        手机号: '1'
      },
      auditStatusList: {
        全部: '',
        待审核: '1',
        审核通过: '2'
      },
      statusList: {
        全部: '',
        有效: '1',
        无效: '0'
      },
      typeEnum: {
        1: '手机号',
        2: '微信'
      },
      userNumList: ['全部', 1, 2, 3, 4, '5次以上'],
      pmsNumList: ['全部', 1, 2, 3, 4, 5, 6, 7, '8次以上'],
      listData: [],
      dialogVisible: false,
      dialogRuleForm: {
        words: '',
        wordType: 1,
        remark: ''
      },
      resultTypeEnum: {
        2: '微信',
        1: '手机号'
      }
    }
  },
  computed: {
    createDaterange: {
      get() {
        if (this.ruleForm.beginTime && this.ruleForm.endTime) {
          return [this.ruleForm.beginTime, this.ruleForm.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.ruleForm.beginTime = value[0]
          this.ruleForm.endTime = value[1]
        } else {
          this.ruleForm.beginTime = ''
          this.ruleForm.endTime = ''
        }
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    search() {
      this.currentChange(1)
    },
    resetForm() {
      Object.assign(this.$data.ruleForm, this.$options.data().ruleForm)
      this.currentChange(1)
    },
    getList() {
      const me = this
      getPmsBlackWordPage({
        ...this.ruleForm,
        page: this.page,
        limit: this.limit,
        blackType: me.isBlack ? 1 : 0,
        userNumbers: me.ruleForm.userNumbers === 0 ? '' : me.ruleForm.userNumbers,
        plidNumbers: me.ruleForm.plidNumbers === 0 ? '' : me.ruleForm.plidNumbers
      }).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data || {}
          const list = data.listData || []
          me.listData = list
          me.total = data.total || 0
        }
      })
    },
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 私信
    goPrivacy(data) {
      this.$refs.chatRef.init(data)
    },
    // 操作是否有效状态
    changeState(data, status) {
      console.log(data, status)
      updateWordStatus({
        id: data.id,
        status: status
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
            postLog(
              116,
              data.id,
              '状态变更',
              status ? '状态开启' : '状态关闭',
              '{}',
              `{}`
            )
            this.getList()
          } else {
            data.status = !data.status
            this.$message.error(res.data.msg)
          }
        })
        .catch((err) => {
          data.status = !data.status
          if (err.message) {
            this.$confirm(err.message, {
              confirmButtonText: '我知道了',
              center: true,
              showCancelButton: false
            })
              .then(() => {})
              .catch((err) => {})
          }
        })
    },
    operation(data, type) {
      const typeList = {
        1: '移动到黑名单',
        2: '移动到白名单',
        3: '忽略'
      }
      this.$confirm(`是否确认${typeList[type]}?`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      })
        .then(() => {
          console.log('操作成功')
          updateBlackType({
            id: data.id,
            blackType: type
          })
            .then((res) => {
              if (res.data.code === 0) {
                const ext = res.data.ext || {}
                this.$message.success('操作成功')
                // this.$confirm(ext.failMsg, ext.successMsg, {
                //   confirmButtonText: '我知道了',
                //   center: true,
                //   showCancelButton: false
                // })
                //   .then(() => {})
                //   .catch((err) => {})
                if (type !== 3) {
                  postLog(
                    115,
                    0,
                    '新增',
                    `${typeList[type]}: ${data.word}`,
                    '{}',
                    `{}`
                  )
                }
                this.dialogVisible = false
                this.getList()
              // } else {
              //   this.$message.success(res.data.msg || '操作失败')
              //   this.dialogVisible = false
              //   this.getList()
              }
            })
            .catch((err) => {})
          //   this.$message.error('操作失败')
        })
        .catch((err) => {})
    },
    openAddDialog() {
      this.dialogVisible = true
      this.dialogRuleForm = {
        words: '',
        wordType: 1,
        remark: ''
      }
    },
    confirmation() {
      if (!this.dialogRuleForm.words) return this.$message.error('请输入词库')
      if (!this.dialogRuleForm.wordType)
        return this.$message.error('请选择类型')
      console.log(this.dialogRuleForm)
      AddPmsBlackWordAddWord({
        wordType: this.dialogRuleForm.wordType,
        words: this.dialogRuleForm.words,
        remark: this.dialogRuleForm.remark,
        blackType: 1
      })
        .then((res) => {
          console.log(res)
          if (res.data.code === 0 && res.data.ext !== null) {
            const ext = res.data.ext || {}
            this.$confirm(ext.failMsg, ext.successMsg, {
              confirmButtonText: '我知道了',
              center: true,
              showCancelButton: false
            })
              .then(() => {})
              .catch((err) => {})
            if (ext.successWord) {
              postLog(
                115,
                0,
                '新增',
                `新增黑名单: ${ext.successWord}`,
                '{}',
                `{}`
              )
            }
            this.dialogVisible = false
            this.getList()
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((err) => {})
    },
    // 日志
    goSeeLog() {
      this.$refs.seeLogRef.init('115')
    },
    openLog(data) {
      this.$refs.seeLogRef.init('116', data.id)
    }
  }
}
</script>

<style lang="scss" scoped></style>
