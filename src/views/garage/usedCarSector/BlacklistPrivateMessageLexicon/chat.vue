<template>
  <div>
    <el-dialog v-model="dialogVisible" title="私信对话查询" width="800">
      <el-form :model="dialogRuleForm" label-width="90px" :inline="true">
        <el-form-item label="主用户ID">
          <el-input
            v-model="dialogRuleForm.messUid"
            type="text"
            placeholder="请输入主用户ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="对话用户ID">
          <el-input
            v-model="dialogRuleForm.dialogUid"
            type="text"
            placeholder="请输入对话用户ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
      </el-form>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
      <el-table :data="listData" border height="50vh">
        <el-table-column prop="messUid" align="center" label="主用户ID">
          <template v-slot="{ row }">
            <span v-if="row.messUserGroupId === 25">禁</span>
            {{ row.messUserName }}
            <br />
            <el-button type="primary" link @click="goUseDetail(row.messUid)">{{ row.messUid }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="dialogUid" align="center" label="对话用户ID">
          <template v-slot="{ row }">
            <span v-if="row.dialogGroupId === 25">禁</span
            >{{ row.dialogUserName }} <br />
            <el-button type="primary" link @click="goUseDetail(row.dialogUid)">{{ row.dialogUid }}</el-button>
          </template>
        </el-table-column>
        <el-table-column type="name" align="center" label="查看对话">
          <template v-slot="{ row }">
            <el-button type="primary" link @click="goPrivacy(row)"
              >查看对话</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="pday" label="创建时间" align="center" />
      </el-table>

      <el-pagination
        v-model:current-page="page"
        :page-size="limit"
        :total="total"
        background
        layout="total, prev, pager, next, jumper"
        class="el-pagination-center"
        @current-change="currentChange"
      />
    </el-dialog>
  </div>
</template>

<script>
import { pmsBlackWordPmsPage } from '@/api/usedCarPublish'
export default {
  name: 'BlacklistPrivateMessageLexiconChat',
  data() {
    return {
      page: 1,
      total: 0,
      limit: 20,
      listData: [],
      dialogVisible: false,
      dialogRuleForm: {
        messUid: '',
        dialogUid: ''
      }
    }
  },
  methods: {
    init(data) {
      this.dialogRuleForm = {
        messUid: '',
        dialogUid: ''
      }
      this.listData = []
      this.page = 1
      this.dialogVisible = true
      this.vxPhone = data.word
      this.getList()
    },
    search() {
      this.currentChange(1)
    },
    resetForm() {
      Object.assign(
        this.$data.dialogRuleForm,
        this.$options.data().dialogRuleForm
      )
      this.currentChange(1)
    },
    getList() {
      const me = this
      pmsBlackWordPmsPage({
        ...this.dialogRuleForm,
        page: this.page,
        limit: this.limit,
        vxPhone: me.vxPhone
      }).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data || {}
          const list = data.listData || []
          me.listData = list
          me.total = data.total || 0
        }
      })
    },
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 私信
    goPrivacy(data) {
      this.$router.push({
        name: 'PrivateMessageQuery',
        query: {
          uid: data.messUid,
          toUid: data.dialogUid,
          isSelected: true
        }
      })
    },
    // 用户详情
    goUseDetail(id) {
      this.$router.push({
        name: 'userAccountCorrelationDetail',
        query: {
          id: id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
