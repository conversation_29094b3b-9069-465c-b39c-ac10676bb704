<template>
  <div class="wd-p-5">
    <div class="wd-pb-5">
      <el-button-group>
        <el-button :type="!tabPane ? 'primary' : ''" @click="changeTabPane(0)"
          >待处理</el-button
        >
        <el-button :type="tabPane ? 'primary' : ''" @click="changeTabPane(1)"
          >黑名单</el-button
        >
      </el-button-group>
    </div>
    <listData v-if="!tabPane" ref="listDataRef" />
    <listData v-if="tabPane" :isBlack="true" ref="listDataRef1" />
  </div>
</template>

<script>
import listData from './list.vue'
export default {
  name: 'BlacklistPrivateMessageLexicon',
  components: {
    listData
  },
  data() {
    return {
      tabPane: 0
    }
  },
  activated() {
    this.changeTabPane(this.tabPane)
  },
  methods: {
    changeTabPane(type) {
      this.tabPane = type
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
