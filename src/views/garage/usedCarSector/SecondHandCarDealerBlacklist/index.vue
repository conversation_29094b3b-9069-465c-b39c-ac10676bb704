<template>
  <div class="wd-p-5">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="用户ID">
        <el-input
          v-model="ruleForm.relatedUserId"
          type="text"
          placeholder="请输入用户ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="有效状态">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="ml20">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="openAddDialog">新增</el-button>
          <el-button class="fl-right" type="primary" @click="goSeeLog"
            >操作日志</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <el-table :data="listData" border height="70vh">
      <el-table-column prop="id" align="center" label="序号" width="100" />
      <el-table-column prop="relatedUserId" align="center" label="用户ID" />
      <el-table-column prop="shopFlag" width="80" label="角色" align="center">
        <template v-slot="{ row }">
          {{ row.relatedUserType === 2 ? '商家' : '个人' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="touchNum"
        align="center"
        label="触发私信词库黑名单次数"
      />
      <el-table-column label="有效状态" align="center">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.status"
            @change="(state) => changeState(scope.row, state)"
            :active-value="1"
            :inactive-value="0"
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" align="center" label="备注" />
      <el-table-column
        label="操作"
        align="center"
        :width="!isBlack ? 300 : 105"
      >
        <template v-slot="{ row }">
          <el-button type="primary" link @click="seeList(row)"
            >查看明细</el-button
          >
          <el-button type="primary" link @click="openLog(row)">日志</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" align="center" label="操作人" />
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @current-change="currentChange"
    />
    <el-dialog v-model="dialogListVisible" title="" width="600">
      <el-table :data="dialogVisibleList" border height="40vh">
        <el-table-column label="时间" align="center" prop="pday" />
        <el-table-column prop="vxPhone" align="center" label="触发私信词库" />
        <el-table-column label="对话用户ID" align="center">
          <template v-slot="{ row }">
            <div>
              {{ row.dialogUserName }} <br />
              <el-button type="primary" link @click="goUseDetail(row)">{{ row.dialogUid }}</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="name" align="center" label="查看对话">
          <template v-slot="{ row }">
            <el-button type="primary" link @click="goPrivacy(row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog v-model="dialogVisible" title="新增" width="600">
      <el-form :model="dialogRuleForm" label-width="70px">
        <el-form-item label="用户ID">
          <div class="wd-flex">
            <el-input
              v-model="dialogRuleForm.relatedUserId"
              placeholder="请输入用户ID"
              type="text"
              size="large"
              style="display: inline-block; margin-right: 10px"
            />
            <el-button
              type="primary"
              style="display: inline-block; margin-right: 10px"
              @click="GetUserAccountDetails()"
              >查询用户信息</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="用户名" v-if="dialogRuleForm.username">
           <div>{{ dialogRuleForm.username }}</div>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="dialogRuleForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer text-center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmation"> 确认 </el-button>
      </div>
    </el-dialog>
    <seeLog ref="seeLogRef" />
  </div>
</template>

<script>
import { usedCarspickerOptions } from '@/utils/configData'
import {
  getPmsBlackWordUserPage,
  addBlackUse,
  updateBlackUserStatus,
  pmsBlackWordPmsPage
} from '@/api/usedCarPublish'
import { getUserAccountDetails } from '@/api/user'
import { postLog } from '@/components/seeLog/SaveLog.js'
import seeLog from '@/components/seeLog/SeeLog.vue'

export default {
  name: 'SecondHandCarDealerBlacklistList',
  props: {
    isBlack: {
      type: Boolean,
      default: false
    }
  },
  components: {
    seeLog
  },
  data() {
    return {
      pickerOptions: usedCarspickerOptions,
      logDialogVisible: false,
      ruleForm: {
        relatedUserId: '',
        status: ''
      },
      page: 1,
      total: 0,
      limit: 20,
      statusList: {
        全部: '',
        有效: '1',
        无效: '0'
      },
      listData: [],
      dialogVisible: false,
      dialogRuleForm: {
        relatedUserId: '',
        desc: ''
      },
      dialogListVisible: false,
      dialogVisibleList: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    search() {
      this.currentChange(1)
    },
    resetForm() {
      Object.assign(this.$data.ruleForm, this.$options.data().ruleForm)
      this.currentChange(1)
    },
    getList() {
      const me = this
      getPmsBlackWordUserPage({
        ...this.ruleForm,
        page: this.page,
        limit: this.limit
      }).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data || {}
          const list = data.listData || []
          me.listData = list
          me.total = data.total || 0
        }
      })
    },
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 操作是否有效状态
    changeState(data, status) {
      console.log(data, status)
      updateBlackUserStatus({
        id: data.id,
        status: status
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
            postLog(
              118,
              data.id,
              '状态变更',
              status ? '状态开启' : '状态关闭',
              '{}',
              `{}`
            )
            this.getList()
          } else {
            data.status = !data.status
            this.$message.error(res.data.msg)
          }
        })
        .catch((err) => {
          data.status = !data.status
        })
    },
    openLog(data) {
      this.$refs.seeLogRef.init('118', data.id)
    },
    // 日志
    goSeeLog() {
      this.$refs.seeLogRef.init('117')
    },
    // 查看明细
    seeList(data) {
      this.dialogListVisible = true
      const me = this
      pmsBlackWordPmsPage({
        messUid: data.relatedUserId,
        page: 1,
        limit: 1000
      }).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data || {}
          const list = data.listData || []
          me.dialogVisibleList = list
        }
      })
    },
    // 新增黑名单
    openAddDialog() {
      this.dialogVisible = true
      this.dialogRuleForm = {
        relatedUserId: '',
        remark: ''
      }
    },

    // 获取用户详情
    GetUserAccountDetails() {
      const me = this
      if (!me.dialogRuleForm.relatedUserId) {
        return me.$message.error('请输入用户ID')
      }
      getUserAccountDetails({
        uid: me.dialogRuleForm.relatedUserId
      }).then((response) => {
        if (response.data.code !== 0) {
          return me.$message.error('用户数据获取失败')
        } else {
          console.log(response)
          const data = response.data.data || {}
          me.dialogRuleForm.username = data.username
        }
      })
    },
    confirmation() {
      if (!this.dialogRuleForm.relatedUserId)
        return this.$message.error('请输入用户ID')
      addBlackUse({
        relatedUserId: this.dialogRuleForm.relatedUserId,
        remark: this.dialogRuleForm.remark || ''
      })
        .then((res) => {
          console.log(res)
          if (res.data.code === 0) {
            this.$message.success(res.data.msg || '操作成功')
            this.getList()
            this.dialogVisible = false
            postLog(
              117,
              0,
              '新增',
              `新增二手车贩名单: ${this.dialogRuleForm.relatedUserId}`,
              '{}',
              `{}`
            )
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((err) => {})
    },
    // 私信
    goPrivacy(data) {
      this.$router.push({
        name: 'PrivateMessageQuery',
        query: {
          uid: data.messUid,
          toUid: data.dialogUid,
          isSelected: true
        }
      })
    },
    // 用户详情
    goUseDetail(data) {
      this.$router.push({
        name: 'userAccountCorrelationDetail',
        query: {
          id: data.dialogUid
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
