<template>
  <div class="wd-p-5">
    <el-form :model="ruleForm" :inline="true">
      <el-form-item label="敏感词">
        <el-input
          v-model="ruleForm.words"
          type="text"
          placeholder="请输入敏感词"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="有效状态">
        <el-select v-model="ruleForm.status">
          <el-option
            v-for="(value, index) in statusList"
            :key="index"
            :label="index"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="ml20">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" class="fl-right" @click="openAddDialog"
            >新增</el-button
          >
          <el-button class="fl-right" type="primary" @click="goSeeLog"
            >操作日志</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <el-table :data="listData" border height="70vh">
      <el-table-column type="name" prop="id" align="center" label="ID" />
      <el-table-column type="name" prop="word" align="center" label="敏感词" />
      <el-table-column prop="pmsNum" align="center" label="触发次数" />
      <el-table-column label="有效状态" align="center">
        <template v-slot="scope">
          <el-switch
            v-model="scope.row.status"
            @change="(state) => changeState(scope.row, state)"
            :active-value="1"
            :inactive-value="0"
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" align="center" label="备注" />
      <el-table-column type="name" align="center" label="操作人">
        <template v-slot="{ row }">
          <div>
            {{ row.operatorName || '系统' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        :width="!isBlack ? 300 : 105"
      >
        <template v-slot="{ row }">
          <el-button type="primary" link @click="openLog(row)">日志</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      class="el-pagination-center"
      @current-change="currentChange"
    />
    <el-dialog v-model="dialogVisible" title="新增" width="600">
      <el-form :model="dialogRuleForm" label-width="70px">
        <el-form-item label="词库" required>
          <el-input
            v-model="dialogRuleForm.words"
            type="textarea"
            :rows="3"
            :autosize="{ minRows: 3 }"
            resize="none"
            placeholder="请输入白名单，按,分开"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="dialogRuleForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer text-center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmation"> 确认 </el-button>
      </div>
    </el-dialog>
    <seeLog ref="seeLogRef" />
  </div>
</template>

<script>
import { usedCarspickerOptions } from '@/utils/configData'
import { changeDate, format } from '@/utils/configData/format'
import {
  getPmsBlackWordPage,
  AddPmsBlackWordAddWord,
  updateWordStatus
} from '@/api/usedCarPublish'
import { postLog } from '@/components/seeLog/SaveLog.js'
import seeLog from '@/components/seeLog/SeeLog.vue'

export default {
  name: 'PrivateMessageLibraryWhitelist',
  props: {
    isBlack: {
      type: Boolean,
      default: false
    }
  },
  components: {
    seeLog
  },
  data() {
    return {
      pickerOptions: usedCarspickerOptions,
      logDialogVisible: false,
      ruleForm: {
        name: '',
        status: ''
      },
      page: 1,
      total: 0,
      limit: 20,
      statusList: {
        全部: '',
        有效: '1',
        无效: '0'
      },
      listData: [],
      dialogVisible: false,
      dialogRuleForm: {
        words: '',
        remark: ''
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    search() {
      this.currentChange(1)
    },
    resetForm() {
      this.ruleForm = {
        name: '',
        status: ''
      }
      this.currentChange(1)
    },
    getList() {
      const me = this
      getPmsBlackWordPage({
        ...this.ruleForm,
        page: this.page,
        limit: this.limit,
        blackType: 2
      }).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data || {}
          const list = data.listData || []
          me.listData = list
          me.total = data.total || 0
        }
      })
    },
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 操作是否有效状态
    changeState(data, status) {
      console.log(data, status)
      updateWordStatus({
        id: data.id,
        status: status
      })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success('操作成功')
            postLog(
              116,
              data.id,
              '状态变更',
              status ? '状态开启' : '状态关闭',
              '{}',
              `{}`
            )
            this.getList()
          } else {
            data.status = !data.status
            this.$message.error(res.data.msg)
          }
        })
        .catch((err) => {
          data.status = !data.status
          if (err.message) {
            this.$confirm(err.message, {
              confirmButtonText: '我知道了',
              center: true,
              showCancelButton: false
            })
              .then(() => {})
              .catch((err) => {})
          }
        })
    },
    openAddDialog() {
      this.dialogVisible = true
      this.dialogRuleForm = {
        words: '',
        remark: ''
      }
    },
    confirmation() {
      if (!this.dialogRuleForm.words) return this.$message.error('请输入词库')
      console.log(this.dialogRuleForm)
      AddPmsBlackWordAddWord({
        words: this.dialogRuleForm.words,
        remark: this.dialogRuleForm.remark,
        blackType: 2
      })
        .then((res) => {
          console.log(res)
          if (res.data.code === 0 && res.data.ext !== null) {
            const ext = res.data.ext || {}
            this.$confirm(ext.failMsg, ext.successMsg, {
              confirmButtonText: '我知道了',
              center: true,
              showCancelButton: false
            })
              .then(() => {})
              .catch((err) => {})
            if (ext.successWord) {
              postLog(
                115,
                0,
                '新增',
                `新增白名单: ${ext.successWord}`,
                '{}',
                `{}`
              )
            }
            this.dialogVisible = false
            this.getList()
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((err) => {})
    },
    // 日志
    goSeeLog() {
      this.$refs.seeLogRef.init('115')
    },
    openLog(data) {
      this.$refs.seeLogRef.init('116', data.id)
    }
  }
}
</script>

<style lang="scss" scoped></style>
