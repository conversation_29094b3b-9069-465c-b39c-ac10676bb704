<template>
  <div v-loading="loading" class="set-content-margin">
    <el-form ref="form" :model="form" :inline="true">
      <el-form-item label="用户ID">
        <el-input v-model="form.authorId" placeholder="请输入用户ID"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="currentChange(1)">查询</el-button>
        <el-button type="primary" @click="addRow">新增+</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="listData"
      :data="listData"
      highlight-current-row
      row-key="listData"
      border
      style="width: 100%; overflow-y: auto; height: 75vh"
    >
      <el-table-column prop="uid" label="用户ID" align="center">
      </el-table-column>
      <el-table-column prop="userName" label="用户昵称" align="center">
      </el-table-column>
      <el-table-column prop="reason" label="加入原因" align="center">
      </el-table-column>
      <el-table-column prop="createTime" align="center" label="创建时间">
        <template v-slot="scope">{{
          $filter.format(scope.row.createTime, 'YYYY-MM-DD HH:mm')
        }}</template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template v-slot="scope">
          <el-button type="primary" link @click="deleteRow(scope.row.id)">
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :page-sizes="[10, 20, 40, 60]"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      style="text-align: center; margin-top: 10px"
      @size-change="handleSizeChange"
      @current-change="currentChange"
    />
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      title="新增白名单"
      center
      width="500px"
    >
      <el-form
        ref="dialogForm"
        status-icon
        label-width="100px"
        label-position="left"
      >
        <div class="panel-operation">
          <el-form-item label="用户ID">
            <el-input
              v-model="dialogForm.authorIds"
              placeholder="请输入用户ID"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item label="加入原因">
            <el-input
              type="textarea"
              :rows="3"
              v-model="dialogForm.reason"
              placeholder="请输入加入原因"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <template v-slot:footer>
        <span>
          <el-button type="primary" @click="confirm">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { forwardPickerOptions } from '@/utils/configData'
import {
  getAuthorBlacklist,
  postSaveBlackAuthor,
  postDeleteBlackAuthor
} from '@/api/garage'
export default {
  name: 'content-blacklist',
  components: {},
  data() {
    return {
      pickerOptions: forwardPickerOptions,
      page: 1,
      total: 0,
      limit: 20,
      listData: [],
      form: {
        authorId: ''
      },
      loading: false,
      dialogForm: {
        authorIds: '',
        reason: '' //加入原因
      },
      dialogVisible: false // 是否显示编辑弹框，默认不显示
    }
  },
  computed: {
    daterange: {
      get() {
        if (this.form.startTime && this.form.endTime) {
          return [this.form.startTime, this.form.endTime]
        }
        return []
      },
      set(value) {
        if (value) {
          this.form.startTime = value[0]
          this.form.endTime = value[1]
        } else {
          this.form.startTime = ''
          this.form.endTime = ''
        }
      }
    },
    ...mapGetters(['name'])
  },
  activated() {
    this.getList()
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      Object.assign(this.dialogForm, this.$options.data().dialogForm)
    },
    //添加数据
    addRow() {
      this.dialogVisible = true
    },
    confirm() {
      const me = this
      postSaveBlackAuthor({
        ...this.dialogForm,
        uid: this.dialogForm.authorIds
      })
        .then((response) => {
          if (response.data.code === 0) {
            me.$message.success('操作成功')
            me.handleClose()
            me.getList()
          } else {
            me.$message.error(response.data.msg || '操作失败')
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },

    // 获取列表
    getList() {
      const me = this
      // me.loading = true
      const postData = {
        ...me.form,
        uid: this.form.authorId,
        page: me.page,
        limit: me.limit
      }
      getAuthorBlacklist(postData)
        .then((response) => {
          console.log(response)
          if (response.data.code === 0) {
            const data = response.data.data || {}
            me.listData = data.listData || []
            const total = data.total
            me.total = total
          }
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          me.loading = false
        })
    },

    //移除
    deleteRow(id) {
      const me = this
      this.$confirm('是否确认删除此项？').then(() => {
        postDeleteBlackAuthor({ id })
          .then((response) => {
            if (response.data.code === 0) {
              me.$message.success('操作成功')
              me.getList()
            } else {
              me.$message.error(response.data.msg || '操作失败')
            }
          })
          .catch((error) => {
            console.log(error)
          })
      })
    },
    // 更新页码
    currentChange(page) {
      this.page = page
      this.getList()
    },
    // 变更查询个数
    handleSizeChange(limit) {
      this.limit = limit
      this.getList()
    }
  }
}
</script>
