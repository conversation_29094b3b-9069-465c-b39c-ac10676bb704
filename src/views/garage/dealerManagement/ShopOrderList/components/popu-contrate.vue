<template>
  <el-dialog v-model="dialogVisible" width="20%" :before-close="handleClose">
    <div v-if="hasResult" class="result">
      <div v-if="resultStatus === RESULT_STATUS.SUCCESS">
        <el-result icon="success" title="合同生成成功" />
      </div>
      <div v-else>
        <el-result icon="error" title="合同生成失败" />
      </div>
    </div>
    <div v-else v-loading="resultStatus === RESULT_STATUS.GENERATING">
      <div class="radio-group">
        <el-radio v-model="contrateRadio" style="margin: 0 0 10px 0" :label="1"
          >公司合同</el-radio
        >
        <el-radio v-model="contrateRadio" :label="2">个体合同</el-radio>
      </div>
      <div class="text-center mt20">
        <el-radio-group v-model="singalRadio" v-if="contrateRadio === 2">
          <el-radio :label="2">纸质合同</el-radio>
          <el-radio :label="3">电子签</el-radio>
        </el-radio-group>
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <el-button class="mr40" @click="handleClose">取 消</el-button>
        <el-button v-if="!hasResult" type="primary" @click="generate"
          >去生成</el-button
        >
        <el-button
          v-if="resultStatus === RESULT_STATUS.FAIL"
          type="primary"
          @click="generate"
          >重新生成</el-button
        >
        <el-button
          v-if="resultStatus === RESULT_STATUS.SUCCESS"
          type="primary"
          @click="download"
          >下载合同</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getShopContrateExport } from '@/api/garage'

const RESULT_STATUS = {
  SUCCESS: 1,
  FAIL: 2,
  GENERATING: 3
}

const dialogVisible = ref(false)
const contrateRadio = ref()
const singalRadio = ref()
const resultStatus = ref()
const item = ref({})
const downloadUrl = ref('')

const hasResult = computed(() => {
  return [RESULT_STATUS.SUCCESS, RESULT_STATUS.FAIL].includes(
    resultStatus.value
  )
})

const init = (row) => {
  item.value = row
  dialogVisible.value = true
}

const reset = () => {
  contrateRadio.value = null
  singalRadio.value = null
  resultStatus.value = null
  item.value = {}
  downloadUrl.value = ''
}

const handleClose = () => {
  reset()
  dialogVisible.value = false
}

const generate = () => {
  if (!contrateRadio.value) {
    return ElMessage.error('请选择合同类型')
  }
  if (contrateRadio.value === 2 && !singalRadio.value) {
    return ElMessage.error('请选择个体合同类型')
  }
  resultStatus.value = RESULT_STATUS.GENERATING
  const contractType =
    contrateRadio.value === 2 ? singalRadio.value : contrateRadio.value
  getShopContrateExport({
    orderNumSub: item.value.orderNumber,
    contractType: contractType
  })
    .then((res) => {
      if (res.data.code === 0) {
        ElMessage.success('生成成功')
        downloadUrl.value = res.data.data || ''
        resultStatus.value = RESULT_STATUS.SUCCESS
      } else {
        resultStatus.value = RESULT_STATUS.FAIL
      }
    })
    .catch((err) => {
      resultStatus.value = RESULT_STATUS.FAIL
      console.log(err)
    })
}

const download = () => {
  function downloadFile(url, filename) {
    const link = document.createElement('a')
    link.href = url
    link.download = filename || 'downloaded-file' // 默认文件名
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  if (downloadUrl.value) {
    downloadFile(downloadUrl.value, '')
  } else {
    ElMessage.error('下载失败')
  }
}

defineExpose({
  init
})
</script>
<style lang="scss" scoped>
.radio-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
