<template>
  <div class="moon-test">
    <el-form :inline="true" :model="ruleForm">
      <el-form-item label="用户ID">
        <el-input v-model="ruleForm.userId" clearable style="width: 220px" />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="ruleForm.businessDate"
          type="month"
          value-format="YYYY-MM"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item label="考核结果">
        <el-select v-model="ruleForm.assessResult" clearable>
          <el-option
            v-for="(label, key) in evaluationResult"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="exportExcel">导出</el-button>
        <el-button @click="systemNotification">发送系统通知</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      max-height="76vh"
      @sort-change="sortSearchList"
    >
      <el-table-column
        prop="businessDate"
        label="月份"
        align="center"
        width="120"
      />
      <el-table-column prop="userId" label="用户ID" align="center" />
      <el-table-column prop="userName" label="用户昵称" align="center" />
      <el-table-column prop="hoopName" label="圈子名称" align="center" />
      <el-table-column
        prop="activeCnt"
        label="圈主月活"
        sortable
        align="center"
      />
      <el-table-column
        prop="operateCnt"
        label="有效操作次数"
        sortable
        align="center"
      />
      <el-table-column
        prop="rejectOperateCnt"
        label="驳回操作次数"
        sortable
        align="center"
      />
      <el-table-column
        prop="contentCnt"
        label="圈内发布数"
        sortable
        align="center"
      />
      <el-table-column prop="operateRate" label="操作率" align="center" />
      <el-table-column prop="reportCnt" label="被投诉成功数" align="center" />
      <el-table-column label="考核结果" align="center" width="120">
        <template v-slot="{ row }">
          <el-select
            v-model="row.assessResult"
            @change="changeAssessResult(row)"
            :placeholder="`&nbsp;`"
          >
            <el-option
              v-for="(label, key) in evaluationResult"
              :key="key"
              :label="label"
              :value="Number(key)"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
      style="justify-content: center; margin-top: 15px"
    />
    <el-dialog
      v-model="dialogVisible"
      :title="`考核结果通知 ${timeData.year}年${timeData.month}月`"
      width="600"
    >
      <div class="result">
        <!-- 优秀圈主奖品 -->
        <div class="fine">
          <div class="title">
            <span>优秀圈主 - {{ fineEnergyAwardNum }}位</span>
          </div>
          <div class="flex">
            <p class="mr10">能量</p>
            <el-input
              v-model="fineEnergy"
              type="number"
              placeholder="发送能量"
              style="width: calc(80% - 38px)"
              @blur="
                addEnergy({
                  type: 1,
                  energy: fineEnergy,
                  message: fineEnergyAwardMessageContent
                })
              "
              clearable
            >
            </el-input>
          </div>
          <div class="real">
            <p class="bold">实物奖品</p>
            <div class="flex">
              <p class="mr10">奖品名称</p>
              <el-input
                v-model="fineEnergyAwardName"
                placeholder="请输入奖品名称"
                style="width: calc(80% - 66px)"
                @blur="
                  addEnergy({
                    type: 4,
                    message: fineEnergyAwardMessageContent,
                    awardName: fineEnergyAwardName,
                    awardPic: fineEnergyAwardImage
                  })
                "
                clearable
              >
              </el-input>
            </div>
            <div class="upload-container">
              <el-upload
                class="image-uploader real-img mt10"
                :http-request="httpRequestOrder"
                :on-success="handleImageSuccess"
                :show-file-list="false"
                :limit="1"
                name="activefile"
                action
                accept="image/*"
              >
                <div
                  class="upload-area"
                  :class="{ 'has-image': fineEnergyAwardImage }"
                >
                  <img
                    v-if="fineEnergyAwardImage"
                    :src="fineEnergyAwardImage"
                    class="uploaded-image"
                    alt="奖品图片"
                  />
                  <div v-else class="upload-placeholder">
                    <el-icon><Plus /></el-icon>
                    <div class="upload-text">点击上传图片</div>
                  </div>
                  <div v-if="fineEnergyAwardImage" class="image-overlay">
                    <el-icon
                      class="overlay-icon"
                      @click.stop="replaceAwardImage"
                      ><Edit
                    /></el-icon>
                    <el-icon class="overlay-icon" @click.stop="deleteAwardImage"
                      ><Delete
                    /></el-icon>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
          <div class="message">
            <p class="bold">系统通知</p>
            <el-input
              :rows="2"
              v-model="fineEnergyAwardMessageContent"
              type="textarea"
              maxlength="500"
              style="width: 80%"
              placeholder="上月圈子月报已生成，快来查看成果吧！"
              @blur="
                addEnergy({
                  type: 1,
                  energy: fineEnergy,
                  message: fineEnergyAwardMessageContent
                })
              "
              clearable
            ></el-input>
          </div>
        </div>

        <!-- 良好圈主奖品 -->
        <div class="good">
          <div class="title">
            <span>良好圈主 - {{ goodEnergyAwardNum }}位</span>
          </div>
          <div class="flex">
            <p class="mr10">能量</p>
            <el-input
              v-model="goodEnergy"
              type="number"
              placeholder="发送能量"
              style="width: calc(80% - 38px)"
              @blur="
                addEnergy({
                  type: 2,
                  energy: goodEnergy,
                  message: goodEnergyAwardMessageContent
                })
              "
              clearable
            >
            </el-input>
          </div>
          <div class="message">
            <p class="bold">系统通知</p>
            <el-input
              :rows="2"
              type="textarea"
              v-model="goodEnergyAwardMessageContent"
              maxlength="500"
              style="width: 80%"
              placeholder="上月圈子月报已生成，快来查看成果吧！"
              @blur="
                addEnergy({
                  type: 2,
                  energy: goodEnergy,
                  message: goodEnergyAwardMessageContent
                })
              "
              clearable
            ></el-input>
          </div>
        </div>

        <!-- 合格圈主奖品 -->
        <div class="pass">
          <div class="title">
            <span>合格圈主 - {{ passEnergyAwardNum }}位</span>
          </div>
          <div class="flex">
            <p class="mr10">能量</p>
            <el-input
              v-model="passEnergy"
              type="number"
              placeholder="发送能量"
              style="width: calc(80% - 38px)"
              @blur="
                addEnergy({
                  type: 3,
                  energy: passEnergy,
                  message: passEnergyAwardMessageContent
                })
              "
              clearable
            >
            </el-input>
          </div>
          <div class="message">
            <p class="bold">系统通知</p>
            <el-input
              :rows="2"
              v-model="passEnergyAwardMessageContent"
              type="textarea"
              maxlength="500"
              style="width: 80%"
              placeholder="上月圈子月报已生成，快来查看成果吧！"
              @blur="
                addEnergy({
                  type: 3,
                  energy: passEnergy,
                  message: passEnergyAwardMessageContent
                })
              "
              clearable
            ></el-input>
          </div>
        </div>
      </div>

      <div :class="['dialog-footer', { 'hide-add': !showAdd }]">
        <el-button class="button" @click="dialogVisible = false">
          取消
        </el-button>
        <el-button
          v-if="showAdd"
          class="button"
          type="primary"
          @click="pushResult"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getBendUserHoopCircleList,
  updBendUserHoopCircleAssessResult,
  pushAssessResult,
  getAwardList,
  postAwardSave
} from '@/api/circle'
import { ElMessage } from 'element-plus'
import { tools } from '@haluo/util'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance() as any

const evaluationResult: any = {
  1: '优秀',
  2: '良好',
  3: '合格',
  4: '不合格'
}

const ruleForm = reactive<any>({
  userId: '',
  assessResult: '',
  businessDate: proxy.$dayjs().format('YYYY-MM'),
  sort: '' // 排序  1月活升序  2月活降序  3操作次数升序  4操作次数降序  5发布数升序  6发布数降序
})
const tableData = ref<any>([])
const page = ref(1)
const limit = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const timeData = reactive<any>({
  year: '',
  month: ''
})
const showAdd = ref(true)

const fineEnergy = ref(0)
const goodEnergy = ref(0)
const passEnergy = ref(0)

const fineEnergyAwardNum = ref(0)
const goodEnergyAwardNum = ref(0)
const passEnergyAwardNum = ref(0)

const fineEnergyAwardMessageContent = ref('')
const goodEnergyAwardMessageContent = ref('')
const passEnergyAwardMessageContent = ref('')

const fineEnergyAwardName = ref('')
const fineEnergyAwardImage = ref('')

let _uploads: Array<any> = []
// 上传图片，同步上传
const httpRequestOrder = async (option: any) => {
  option.imageType = 'nowater' // 无水印
  option.quality = 1 // 不压缩
  _uploads = _uploads || []
  _uploads.push({
    fn: proxy.$oss.ossUploadImage,
    option
  })
  tools.debounce(call, 100)()
}
const call = async () => {
  for (const a of _uploads) {
    await a.fn(a.option)
  }
  _uploads = []
}
// 修改图片
const handleImageSuccess = (file: any) => {
  if (!file) return
  if (file && file.imgOrgUrl) {
    fineEnergyAwardImage.value = file.imgOrgUrl

    addEnergy({
      type: 4,
      message: fineEnergyAwardMessageContent.value,
      awardName: fineEnergyAwardName.value,
      awardPic: fineEnergyAwardImage.value
    })
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 替换奖品图片
const replaceAwardImage = () => {
  // 触发文件选择
  const uploadEl = document.querySelector(
    '.real-img input[type="file"]'
  ) as HTMLInputElement
  if (uploadEl) {
    uploadEl.click()
  }
}

// 删除奖品图片
const deleteAwardImage = () => {
  fineEnergyAwardImage.value = ''
}

onMounted(() => {
  getList()
})

const search = () => {
  currentChange(1)
}

const getList = () => {
  getBendUserHoopCircleList({
    ...ruleForm,
    assessResult: ruleForm.assessResult || 0,
    page: page.value,
    limit: limit.value
  }).then((res) => {
    if (res.data.code === 0) {
      const data = res.data.data || {}
      tableData.value = data.list || []
      tableData.value.forEach((v: any) => {
        v.assessResult = v.assessResult || ''
      })
      total.value = data.total || 0
    }
  })
}

const currentChange = (num: number) => {
  page.value = num
  getList()
}

const sortSearchList = ({ order, prop }: any) => {
  if (prop === 'activeCnt') {
    ruleForm.sort = order ? (order === 'ascending' ? 1 : 2) : ''
  }
  if (prop === 'operateCnt') {
    ruleForm.sort = order ? (order === 'ascending' ? 3 : 4) : ''
  }
  if (prop === 'contentCnt') {
    ruleForm.sort = order ? (order === 'ascending' ? 5 : 6) : ''
  }
  getList()
}

const changeAssessResult = (data: any) => {
  updBendUserHoopCircleAssessResult({
    id: data.id,
    assessResult: data.assessResult
  })
    .then((res) => {
      if (res.data.code === 0) {
        proxy.$message.success('操作成功')
      } else {
        proxy.$message.error(res.data.msg || '操作失败')
      }
    })
    .finally(() => {
      getList()
    })
}

const awardList = () => {
  // showAdd.value = true
  getAwardList({
    businessDate: ruleForm.businessDate
  })
    .then((res: any) => {
      if (res.data.code === 0) {
        const data = res.data.data || []
        if (data && data.length) {
          // 重置表单
          fineEnergy.value = 0
          goodEnergy.value = 0
          passEnergy.value = 0
          fineEnergyAwardNum.value = 0
          fineEnergyAwardMessageContent.value = ''
          fineEnergy.value = 0
          goodEnergyAwardNum.value = 0
          goodEnergyAwardMessageContent.value = ''
          goodEnergy.value = 0
          passEnergyAwardNum.value = 0
          passEnergyAwardMessageContent.value = ''
          passEnergy.value = 0
          fineEnergyAwardName.value = ''
          fineEnergyAwardImage.value = ''

          data.forEach((v: any) => {
            if (v.awardType === 1) {
              fineEnergyAwardNum.value = v.assessLevelCount || 0
              fineEnergyAwardMessageContent.value = v.messageContent || ''
              fineEnergy.value = v.energy || 0
            }
            if (v.awardType === 2) {
              goodEnergyAwardNum.value = v.assessLevelCount || 0
              goodEnergyAwardMessageContent.value = v.messageContent || ''
              goodEnergy.value = v.energy || 0
            }
            if (v.awardType === 3) {
              passEnergyAwardNum.value = v.assessLevelCount || 0
              passEnergyAwardMessageContent.value = v.messageContent || ''
              passEnergy.value = v.energy || 0
            }
            if (v.awardType === 4) {
              fineEnergyAwardName.value = v.awardName || ''
              fineEnergyAwardImage.value = v.awardPic || ''
            }
            // if (v.messagePush) {
            //   showAdd.value = false
            // }
          })
        }
      }
    })
    .finally(() => {
      dialogVisible.value = true
    })
}

const systemNotification = () => {
  timeData.year = Number(ruleForm.businessDate.split('-')[0])
  timeData.month = Number(ruleForm.businessDate.split('-')[1])
  fineEnergy.value = 0
  goodEnergy.value = 0
  passEnergy.value = 0
  awardList()
}

const pushResult = () => {
  pushAssessResult({
    assessResults: '1,2,3,4',
    businessDate: ruleForm.businessDate
  }).then((res) => {
    if (res.data.code === 0) {
      proxy.$message.success('操作成功')
      dialogVisible.value = false
    } else {
      proxy.$message.error(res.data.msg || '操作失败')
    }
  })
}

const addEnergy = ({ type, energy, message, awardName, awardPic }: any) => {
  if (type === 4) {
    if (!awardName && !awardPic) {
      return
    }
  } else {
    if (energy !== 0 && !energy && !message) {
      return ElMessage.warning('请输入能量值')
    }
  }
  postAwardSave({
    awardType: type,
    businessDate: ruleForm.businessDate,
    messageContent: message || '上月圈子月报已生成，快来查看成果吧！',

    energy: energy,

    awardName: awardName || '',
    awardPic: awardPic || ''
  }).then((res: any) => {
    if (res.data.code === 0) {
      // proxy.$message.success('设置成功')
    } else {
      proxy.$message.error(res.data.msg || '设置失败')
    }
  })
}

const exportExcel = () => {
  proxy
    .$confirm('你确认导出到Excel么', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      const loading = proxy.$loading({
        lock: true,
        text: '正在导出，请稍等......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getBendUserHoopCircleList({
        ...ruleForm,
        assessResult: ruleForm.assessResult || 0,
        page: 1,
        limit: 9999
      })
        .then((res) => {
          if (res.data.code === 0) {
            const data = res.data.data || {}
            dataHandling(data.list || [])
          }
        })
        .finally(() => {
          loading.close()
        })
    })
    .catch(() => {})
}

const dataHandling = async (data: any) => {
  // 导出的表头
  const tHeader = [
    '月份',
    '用户ID',
    '用户昵称',
    '圈子名称',
    '圈主月活',
    '圈主操作次数',
    '圈内发布数',
    '操作率',
    // '话题数',
    '被投诉成功数',
    '考核结果'
  ]
  // 导出表头要对应的数据
  const filterVal = [
    'businessDate',
    'userId',
    'userName',
    'hoopName',
    'activeCnt',
    'operateCnt',
    'contentCnt',
    'operateRate',
    // '',
    'reportCnt',
    'assessResult'
  ]
  const exportData = data.map((value: any) => {
    return filterVal.map((key: string) => {
      if (key === 'assessResult') {
        return evaluationResult[value[key]] || ''
      }
      return value[key]
    })
  })
  const { export_json_to_excel_new }: any = await import(
    '@/vendor/Export2Excel'
  )
  export_json_to_excel_new({
    header: tHeader,
    data: exportData,
    filename: `圈主月度考核`
  })
}
</script>

<style lang="scss" scoped>
.moon-test {
  padding: 20px;
  :deep(.el-dialog) {
    .el-dialog__body {
      padding-top: 15px;
    }
  }
  .result {
    margin-bottom: 20px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      > span {
        margin-right: 5px;
      }
    }

    .fine,
    .good,
    .pass {
      padding: 10px;
      margin-bottom: 20px;
      border: 1px solid #ccc;
    }
  }

  .upload-container {
    :deep(.el-upload) {
      width: auto;
    }
    .image-uploader {
      .upload-area {
        position: relative;
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        overflow: hidden;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }

        &.has-image {
          border: none;
        }

        .uploaded-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #8c939d;

          .el-icon {
            font-size: 28px;
            margin-bottom: 8px;
          }

          .upload-text {
            font-size: 12px;
          }
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          .overlay-icon {
            color: white;
            font-size: 20px;
            margin: 0 8px;
            cursor: pointer;

            &:hover {
              color: #409eff;
            }
          }
        }

        &:hover .image-overlay {
          opacity: 1;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 10%;
    &.hide-add {
      justify-content: center;
    }
    .button {
      width: 35%;
    }
  }
}
</style>
