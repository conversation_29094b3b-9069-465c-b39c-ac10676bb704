<template>
  <div class="get-award-list">
    <el-form :inline="true" :model="ruleForm">
      <el-form-item label="活动名称">
        <el-input
          v-model="ruleForm.actName"
          clearable
          style="width: 220px"
          placeholder="请输入活动名称"
        />
      </el-form-item>
      <el-form-item label="奖品类型">
        <el-select
          v-model="ruleForm.recordType"
          clearable
          placeholder="请选择奖品类型"
        >
          <el-option
            v-for="(label, key) in recordTypeOptions"
            :key="key"
            :label="label"
            :value="Number(key)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="领取时间">
        <el-date-picker
          :default-time="
            ['00:00:00', '23:59:59'].map((d) => $dayjs(d, 'hh:mm:ss').toDate())
          "
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          v-model="dateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 250px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input
          v-model="ruleForm.userId"
          clearable
          style="width: 220px"
          placeholder="请输入用户ID"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="exportExcel">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border max-height="76vh" v-loading="loading">
      <el-table-column prop="actName" label="活动名称" align="center" />
      <el-table-column prop="recordType" label="奖品类型" align="center">
        <template v-slot="{ row }">
          {{ recordTypeOptions[row.recordType] || '' }}
        </template>
      </el-table-column>
      <el-table-column prop="userId" label="用户ID" align="center" />
      <el-table-column prop="userName" label="用户昵称" align="center" />
      <el-table-column prop="goodsName" label="商品信息" align="center" />
      <el-table-column prop="receiveTime" label="领取时间" align="center" />
    </el-table>

    <!-- <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
      style="justify-content: center; margin-top: 15px"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { MANAGERURL } from '@/utils/configData/config'
import {
  getReceiveRealAwardList
  // exportReceiveRealAwardList
} from '@/api/circle'
import { ElMessage } from 'element-plus'
import { forwardPickerOptions as pickerOptions } from '@/utils/configData'

const { proxy } = getCurrentInstance() as any
const store = useStore()

const recordTypeOptions: any = {
  1: '圈主奖品',
  2: '话题奖品'
}

const ruleForm = reactive<any>({
  actName: '',
  recordType: '',
  userId: '',
  startTime: '',
  endTime: ''
})

const dateRange = computed({
  get() {
    if (ruleForm.startTime && ruleForm.endTime) {
      return [ruleForm.startTime, ruleForm.endTime]
    }
    return []
  },
  set(value) {
    if (value && value.length === 2) {
      ruleForm.startTime = value[0]
      ruleForm.endTime = value[1]
    } else {
      ruleForm.startTime = ''
      ruleForm.endTime = ''
    }
  }
})

const tableData = ref<any>([])
const page = ref(1)
const limit = ref(20)
const total = ref(0)
const loading = ref(false)

onMounted(() => {
  getList()
})

const search = () => {
  currentChange(1)
}

const getList = () => {
  loading.value = true
  getReceiveRealAwardList({
    ...ruleForm,
    page: page.value,
    limit: limit.value
  })
    .then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data || {}
        tableData.value = data.list || []
        total.value = data.total || 0
      } else {
        ElMessage.error(res.data.msg || '获取数据失败')
      }
    })
    .catch((err) => {
      // ElMessage.error('获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

const currentChange = (num: number) => {
  page.value = num
  getList()
}

const exportExcel = () => {
  proxy
    .$confirm('你确认导出到Excel么', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      const loading = proxy.$loading({
        lock: true,
        text: '正在导出，请稍等......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 构造查询参数并打开新窗口
      const params = new URLSearchParams()
      Object.keys(ruleForm).forEach((key) => {
        if (ruleForm[key] !== undefined && ruleForm[key] !== '') {
          params.append(key, ruleForm[key])
        }
      })
      params.append('page', '1')
      params.append('limit', '99999')
      params.append('ossUserId', store.getters.uid)
      params.append('ossToken', store.getters.token)
      // mark 使用接口导出示范
      const url = `${MANAGERURL}hoop/oss/goods/award/user/record/export?${params.toString()}`
      window.open(url, '_blank')
      loading.close()
      ElMessage.success('导出成功')
    })
    .catch(() => {})
}

const dataHandling = async (data: any) => {
  // 导出的表头
  const tHeader = [
    '活动名称',
    '奖品类型',
    '用户ID',
    '用户昵称',
    '商品信息',
    '领取时间'
  ]
  // 导出表头要对应的数据
  const filterVal = [
    'actName',
    'recordType',
    'userId',
    'userName',
    'goodsName',
    'receiveTime'
  ]
  const exportData = data.map((value: any) => {
    return filterVal.map((key: string) => {
      if (key === 'recordType') {
        return recordTypeOptions[value[key]] || ''
      }
      return value[key] || ''
    })
  })
  const { export_json_to_excel_new }: any = await import(
    '@/vendor/Export2Excel'
  )
  export_json_to_excel_new({
    header: tHeader,
    data: exportData,
    filename: `实物奖品领取列表`
  })
}
</script>

<style lang="scss" scoped>
.get-award-list {
  padding: 20px;
}
</style>
