<template>
  <div class="award-list">
    <div class="header">
      <el-button type="primary" @click="openDialog()">新增</el-button>
    </div>

    <el-table :data="tableData" border max-height="76vh" v-loading="loading">
      <el-table-column prop="id" label="商品ID" align="center" width="120" />
      <el-table-column prop="goodsName" label="商品名称" align="center" />
      <el-table-column
        prop="goodsPic"
        label="商品图片"
        align="center"
        width="120"
      >
        <template v-slot="{ row }">
          <img
            v-if="row.goodsPic"
            :src="row.goodsPic"
            style="
              width: 100px;
              height: 60px;
              object-fit: cover;
              cursor: pointer;
            "
            @click="previewImage(row.goodsPic)"
            alt="商品图片"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="stock"
        label="当前库存"
        align="center"
        width="120"
      />
      <el-table-column
        prop="status"
        label="有效状态"
        align="center"
        width="120"
      >
        <template v-slot="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="changeStatus(row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="180"
      />
      <el-table-column label="操作" align="center" width="180">
        <template v-slot="{ row }">
          <el-button type="primary" size="small" @click="openDialog(row)"
            >编辑</el-button
          >
          <el-button type="danger" size="small" @click="deleteItem(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      :page-size="limit"
      :total="total"
      background
      layout="total, prev, pager, next, jumper"
      @current-change="currentChange"
      style="justify-content: center; margin-top: 15px"
    />

    <!-- 新增/编辑商品对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑商品' : '新增商品'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="formData.goodsName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品库存" prop="stock">
          <el-input-number
            v-model="formData.stock"
            :min="0"
            placeholder="请输入商品库存"
          />
        </el-form-item>
        <el-form-item label="商品图片" prop="goodsPic">
          <div class="upload-container">
            <el-upload
              class="image-uploader"
              :http-request="httpRequestOrder"
              :on-success="handleImageSuccess"
              :show-file-list="false"
              :limit="1"
              name="activefile"
              action
              accept="image/*"
            >
              <div
                class="upload-area"
                :class="{ 'has-image': formData.goodsPic }"
              >
                <img
                  v-if="formData.goodsPic"
                  :src="formData.goodsPic"
                  class="uploaded-image"
                  alt="商品图片"
                />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div class="upload-text">点击上传图片</div>
                </div>
                <div v-if="formData.goodsPic" class="image-overlay">
                  <el-icon class="overlay-icon" @click.stop="replaceImage"
                    ><Edit
                  /></el-icon>
                  <el-icon class="overlay-icon" @click.stop="deleteImage"
                    ><Delete
                  /></el-icon>
                </div>
              </div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSave">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getRealAwardList, AddRealAward, updateRealAward } from '@/api/circle'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { tools } from '@haluo/util'

const { proxy } = getCurrentInstance() as any

const tableData = ref<any>([])
const page = ref(1)
const limit = ref(50)
const total = ref(0)
const loading = ref(false)

const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const formData = reactive<any>({
  id: '',
  goodsName: '',
  goodsPic: '',
  stock: 0
})

const rules = {
  goodsName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  stock: [{ required: true, message: '请输入商品库存', trigger: 'blur' }]
}

let _uploads: Array<any> = []

onMounted(() => {
  getList()
})

const getList = () => {
  loading.value = true
  getRealAwardList({
    page: page.value,
    limit: limit.value
  })
    .then((res) => {
      if (res.data.code === 0) {
        tableData.value = res.data.data || []
        total.value = res.data.total || 0
      } else {
        ElMessage.error(res.data.msg || '获取数据失败')
      }
    })
    .catch((err) => {
      // ElMessage.error('获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

const currentChange = (num: number) => {
  page.value = num
  getList()
}

const openDialog = (row?: any) => {
  isEdit.value = !!row
  if (row) {
    formData.id = row.id
    formData.goodsName = row.goodsName
    formData.goodsPic = row.goodsPic
    formData.stock = row.stock
  } else {
    resetForm()
  }
  dialogVisible.value = true
}

const resetForm = () => {
  formData.id = ''
  formData.goodsName = ''
  formData.goodsPic = ''
  formData.stock = 0
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const changeStatus = (row: any) => {
  updateRealAward({
    id: row.id,
    status: row.status
  })
    .then((res) => {
      if (res.data.code === 0) {
        ElMessage.success('状态更新成功')
      } else {
        ElMessage.error(res.data.msg || '状态更新失败')
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    })
    .catch((err) => {
      // ElMessage.error('状态更新失败')
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
    })
}

const deleteItem = (row: any) => {
  ElMessageBox.confirm('确认删除该商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      updateRealAward({
        id: row.id,
        isDelete: 1
      })
        .then((res) => {
          if (res.data.code === 0) {
            ElMessage.success('删除成功')
            getList()
          } else {
            ElMessage.error(res.data.msg || '删除失败')
          }
        })
        .catch((err) => {
          // ElMessage.error('删除失败')
        })
    })
    .catch(() => {})
}

const confirmSave = () => {
  if (!formRef.value) return
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      const params: any = {
        goodsName: formData.goodsName,
        goodsPic: formData.goodsPic,
        stock: formData.stock
      }
      if (isEdit.value) {
        params.id = formData.id
      }
      let method = isEdit.value ? updateRealAward : AddRealAward

      method(params)
        .then((res) => {
          if (res.data.code === 0) {
            ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
            dialogVisible.value = false
            getList()
          } else {
            ElMessage.error(
              res.data.msg || (isEdit.value ? '编辑失败' : '新增失败')
            )
          }
        })
        .catch((err) => {
          // ElMessage.error(isEdit.value ? '编辑失败' : '新增失败')
        })
    }
  })
}

// 上传图片，同步上传
const httpRequestOrder = async (option: any) => {
  option.imageType = 'nowater' // 无水印
  option.quality = 1 // 不压缩
  _uploads = _uploads || []
  _uploads.push({
    fn: proxy.$oss.ossUploadImage,
    option
  })
  tools.debounce(call, 100)()
}

const call = async () => {
  for (const a of _uploads) {
    await a.fn(a.option)
  }
  _uploads = []
}

// 图片上传成功
const handleImageSuccess = (file: any) => {
  if (!file) return
  if (file && file.imgOrgUrl) {
    formData.goodsPic = file.imgOrgUrl
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 替换图片
const replaceImage = () => {
  // 触发文件选择
  const uploadEl = document.querySelector(
    '.image-uploader input[type="file"]'
  ) as HTMLInputElement
  if (uploadEl) {
    uploadEl.click()
  }
}

// 删除图片
const deleteImage = () => {
  formData.goodsPic = ''
}

// 预览图片
const previewImage = (imageUrl: string) => {
  proxy.$viewerApi({
    images: [imageUrl],
    options: {
      initialViewIndex: 0
    }
  })
}
</script>

<style lang="scss" scoped>
.award-list {
  padding: 20px;

  .header {
    margin-bottom: 20px;
  }

  .upload-container {
    .image-uploader {
      .upload-area {
        position: relative;
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        overflow: hidden;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }

        &.has-image {
          border: none;
        }

        .uploaded-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #8c939d;

          .el-icon {
            font-size: 28px;
            margin-bottom: 8px;
          }

          .upload-text {
            font-size: 12px;
          }
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          .overlay-icon {
            color: white;
            font-size: 20px;
            margin: 0 8px;
            cursor: pointer;

            &:hover {
              color: #409eff;
            }
          }
        }

        &:hover .image-overlay {
          opacity: 1;
        }
      }
    }
  }

  .dialog-footer {
    text-align: center;
  }
}
</style>
