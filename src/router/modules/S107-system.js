import * as Vue from 'vue' /** When your routing table is too long, you can split it into small modules**/

import menu from '@/router/menuIds'

const system = {
  path: '/system',
  component: () => import('@/views/layout/Layout.vue'),
  redirect: 'noredirect',
  meta: { title: '系统处理', icon: 'lock', menuId: 'S107' },
  children: [
    {
      path: 'versionManagement',
      component: () => import('@/views/system/versionManagement/index.vue'),
      name: 'VersionManagement',
      meta: {
        title: '版本管理',
        icon: 'list',
        noCache: true,
        menuId: 'S10701'
      }
    },
    {
      path: 'VersionEdit',
      component: () =>
        import('@/views/system/versionManagement/versionEdit/index.vue'),
      name: 'VersionEdit',
      meta: {
        title: '版本编辑',
        icon: 'list',
        noCache: true,
        menuId: menu.noLimit
      },
      hidden: true
    },
    {
      path: 'androidChannelManagement',
      component: () =>
        import('@/views/system/androidChannelManagement/index.vue'),
      name: 'AndroidChannelManagement',
      meta: {
        title: '安卓渠道管理',
        icon: 'list',
        noCache: true,
        menuId: 'S10711'
      }
    },
    {
      path: 'AllFeedbackProcessing',
      component: () => import('@/views/system/feedbackProcessing/index.vue'),
      name: 'AllFeedbackProcessing',
      meta: {
        title: '反馈处理',
        icon: 'list',
        noCache: true,
        menuId: 'S10702'
      }
    },
    {
      path: 'FeedbackProcessing',
      component: () => import('@/views/system/feedbackProcessing/index.vue'),
      name: 'FeedbackProcessing',
      meta: {
        title: '反馈处理（除车库）',
        icon: 'list',
        noCache: true,
        menuId: 'S10709'
      }
    },
    {
      path: 'dealerFeedback',
      component: () => import('@/views/system/dealerFeedback/index.vue'),
      name: 'DealerFeedback',
      meta: {
        title: '商家端APP经销商反馈',
        icon: 'list',
        noCache: true,
        menuId: 'S10706'
      }
    },
    {
      path: 'MarketComment',
      component: () => import('@/views/system/marketComment/index.vue'),
      name: 'MarketComment',
      meta: {
        title: '市场点评',
        icon: 'list',
        noCache: true,
        menuId: 'S10703'
      }
    },
    {
      path: 'test',
      component: () => import('@/views/system/test/index.vue'),
      name: 'Test',
      meta: {
        title: '测试专用',
        icon: 'list',
        noCache: true,
        menuId: 'S10704'
      }
    },
    {
      path: 'testNew',
      component: () => import('@/views/system/test/testNew.vue'),
      name: 'testNew',
      meta: {
        title: '查询用户信息',
        icon: 'list',
        noCache: true,
        menuId: 'S10715'
      }
    },
    {
      path: 'downorgimg',
      component: () => import('@/views/system/downorgimg/index.vue'),
      name: 'downorgimg',
      meta: {
        title: '原图下载',
        icon: 'list',
        noCache: true,
        menuId: 'S10705'
      }
    },
    {
      path: 'num',
      component: () => import('@/views/system/num/index.vue'),
      name: 'Num',
      meta: {
        title: '买号专用',
        icon: 'list',
        noCache: true,
        menuId: 'S10706'
      }
    },
    {
      path: 'RevealNum',
      component: () => import('@/views/system/revealNum/index.vue'),
      name: 'RevealNum',
      meta: {
        title: '揭秘手机号',
        icon: 'list',
        noCache: true,
        menuId: 'S10707'
      }
    },
    {
      path: 'VersionEditor',
      component: () => import('@/views/system/versionEditor/index.vue'),
      name: 'VersionEditor',
      meta: {
        title: '协议配置管理(弹框)',
        icon: 'list',
        noCache: true,
        menuId: 'S10708'
      }
    },
    {
      path: 'protocolConfig',
      component: () => import('@/views/system/protocolConfig/index.vue'),
      name: 'ProtocolConfig',
      meta: {
        title: '协议配置管理',
        icon: 'list',
        noCache: true,
        menuId: 'S10716'
      }
    },
    {
      path: 'appConfig',
      component: () => import('@/views/system/appConfig/index.vue'),
      name: 'AppConfig',
      meta: { title: 'APP配置', icon: 'list', noCache: true, menuId: 'S10710' }
    },
    {
      path: 'maching',
      component: () => import('@/views/system/maching/index.vue'),
      name: 'maching',
      meta: {
        title: '机器人管理',
        icon: 'list',
        noCache: true,
        menuId: 'S10713'
      }
    },
    {
      path: 'warning',
      component: () => import('@/views/system/warning/index.vue'),
      name: 'warning',
      meta: { title: '报警管理', icon: 'list', noCache: true, menuId: 'S10712' }
    },
    {
      path: 'androidCertificate',
      component: () => import('@/views/system/androidCertificate/index.vue'),
      name: 'androidCertificate',
      meta: { title: '安卓证书', icon: 'list', noCache: true, menuId: 'S10714' }
    },
    {
      path: 'CertificateWarning',
      component: () => import('@/views/system/certificateWarning/index.vue'),
      name: 'CertificateWarning',
      meta: { title: '证书提醒', icon: 'list', noCache: true, menuId: 'S10717' }
    }
  ]
}

export default system
