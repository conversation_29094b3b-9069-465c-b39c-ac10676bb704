#!/bin/bash

# 记录当前分支
current_branch=$(git branch --show-current)

# 切换到 release 分支
git checkout release

# 拉取 release 分支的最新代码
git pull origin release

# 合并当前分支到 release 分支
git merge --no-edit "$current_branch"

# 推送合并后的代码到远程 release 分支
git push origin release

# 切换到 develop 分支
git checkout develop

# 拉取 develop 分支的最新代码
git pull origin develop

# 合并当前分支到 develop 分支
git merge --no-edit "$current_branch"

# 推送合并后的代码到远程 develop 分支
git push origin develop

# 切回原来的分支
git checkout "$current_branch"

echo "已将 $current_branch 合并到 release 分支，现在已切回 $current_branch 分支。"   